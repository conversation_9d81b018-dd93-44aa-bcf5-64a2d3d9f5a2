import {useIsFocused} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {VictoryPie} from 'victory-native';
import {AppActions} from '../../../app.slice';
import {formatVND} from '../../../services/util';
import {AccountSelectors} from '../../account/services/account.slice';
import {PosActions} from '../../sale-location/services/pos.slice';
import Sys from '../../../components/Sys';

const KPIBranchManager = () => {
  const isFocused = useIsFocused();
  const profile = useSelector(AccountSelectors.profile);
  const [data, setData] = useState([]);
  const [kpi, setKpi] = useState(undefined);
  const dispatch = useDispatch();

  const globalFilter = useSelector(AccountSelectors.globalFilter);

  useEffect(() => {
    if (isFocused) {
      const date = moment().subtract(1, 'days');
      const quarter = Math.ceil((date.month() + 1) / 3);
      const year = date.year();
      dispatch(
        AppActions.fetch({
          onFail: rs => {
            console.log(rs.response.data);
          },
          onSuccess: rs => {
            setKpi(rs.data.stats);
          },
          url: 'revplan/branch/kpis/quarter',
          params: {
            year,
            quarter,
          },
        }),
      );
    }
  }, [data, isFocused, globalFilter]);

  useEffect(() => {
    if (!!profile && isFocused) {
      dispatch(
        PosActions.getPosByURL({
          url: `pos?plan_month=${moment()
            .subtract(1, 'days')
            .format('YYYY-MM')}&checkin_status=1&staff_id=${
            profile?.id
          }&pageSize=-1&with[]=agency&with[]=staff`,
          onSuccess: rs => {
            setData(rs);
          },
        }),
      );
    }
  }, [profile, isFocused]);

  const cal = () => {
    try {
      return kpi?.actual_revenue / kpi?.revenue_amount;
    } catch (error) {
      return '--';
    }
  };

  return (
    <View
      style={{
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 15,
      }}>
      <View
        style={{
          backgroundColor: 'white',
          padding: 10,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
          }}>
          <View>
            <Sys.Text
              style={{
                lineHeight: 16,
                fontWeight: '600',
                marginBottom: 10,
              }}>
              KPI doanh thu quý {Math.ceil((moment().month() + 1) / 3)} lũy kế
              tới ngày {moment().subtract(1, 'days').format('DD/MM/YYYY')}
            </Sys.Text>
          </View>
          {/* <View
            style={{
              flexDirection: 'row',
            }}>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                color: '#90BB3B',
                width: 26,
                marginBottom: 5,
                width: 70,
              }}>
              3 Tỷ
            </Text>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                color: '#848484',
                marginBottom: 5,
              }}>
              doanh thu SMS
            </Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
            }}>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                width: 70,
                marginBottom: 5,
              }}>
              3 Tỷ
            </Text>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                color: '#848484',
              }}>
              doanh thu POS
            </Text>
          </View>
           */}

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                color: '#EF7721',
              }}>
              {formatVND(kpi?.actual_revenue)}
            </Sys.Text>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                color: '#848484',
              }}>
              DT thực tế
            </Sys.Text>
          </View>

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                color: '#EF7721',
              }}>
              {formatVND(kpi?.revenue_amount)}
            </Sys.Text>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                color: '#848484',
              }}>
              DT kế hoạch
            </Sys.Text>
          </View>
          {/* <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                color: '#EF7721',
              }}>
              {formatVND(kpi?.net_tickets_sold)}
            </Text>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                color: '#848484',
              }}>
              vé bán
            </Text>
          </View> */}
        </View>
        <View
          style={{
            flex: 1,
            height: 100,
            alignItems: 'center',
          }}>
          <View
            style={{
              position: 'absolute',
              top: -30,
            }}>
            <VictoryPie
              cornerRadius={25}
              width={200} // Đặt chiều rộng của biểu đồ
              height={200} // Đặt chiều cao của biểu đồ
              data={[
                {x: 1, y: 1}, // Phần dữ liệu chính
                {x: 2, y: 0}, // Đặt thành 0 để không hiển thị
              ]}
              colorScale={['#E2E2EA', 'transparent']} // Màu xám cho phần nhỏ
              innerRadius={65}
              startAngle={-100} // Bắt đầu từ 90 độ, nơi phần xanh kết thúc
              endAngle={100} // Kết thúc cùng một điểm, tạo bo tròn
              style={{
                labels: {fill: 'none'},
              }}
            />
          </View>
          <View
            style={{
              position: 'absolute',
              top: -30,
            }}>
            <VictoryPie
              cornerRadius={25}
              width={200} // Đặt chiều rộng của biểu đồ
              height={200} // Đặt chiều cao của biểu đồ
              data={[
                {x: 1, y: cal()}, // Phần dữ liệu chính
                {x: 2, y: 1 - Number(cal())}, // Đặt thành 0 để không hiển thị
              ]}
              colorScale={['#0062FF', '#E2E2EA']} // Màu xám cho phần nhỏ
              innerRadius={65}
              startAngle={-100} // Bắt đầu từ 90 độ, nơi phần xanh kết thúc
              endAngle={100} // Kết thúc cùng một điểm, tạo bo tròn
              style={{
                labels: {fill: 'none'},
              }}
            />
          </View>
          <View
            style={{
              position: 'absolute',
              bottom: 24,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Sys.Text
              style={{
                color: '#0062FF',
                fontSize: 25,
              }}>
              {cal() === Infinity ? '∞' : (cal() * 100).toFixed(0)}%
            </Sys.Text>
          </View>
          <View
            style={{
              position: 'absolute',
              bottom: 0,
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%',
              paddingHorizontal: 10,
              flexDirection: 'row',
            }}>
            <View style={{}}>
              <Sys.Text
                style={{
                  fontSize: 12,
                  lineHeight: 16,
                  color: '#848484',
                }}>
                0 Tỷ
              </Sys.Text>
            </View>
            <View style={{}}>
              <Sys.Text
                style={{
                  fontSize: 12,
                  lineHeight: 16,
                  color: '#0062FF',
                }}>
                <Sys.Text
                  style={{
                    fontWeight: '600',
                  }}>
                  {formatVND(kpi?.revenue_amount / 1000000000)}
                </Sys.Text>{' '}
                Tỷ
              </Sys.Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default KPIBranchManager;
