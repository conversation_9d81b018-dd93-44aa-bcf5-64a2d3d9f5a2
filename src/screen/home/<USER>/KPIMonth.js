import {useIsFocused, useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {
  Animated,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Svg, {Circle, Defs, LinearGradient as SvgLinearGradient, Stop} from 'react-native-svg';
import {AppActions} from '../../../app.slice';
import {AccountSelectors} from '../../account/services/account.slice';
import DownIcon from '../icons/DownIcon';
import LocationSmallIcon from '../icons/LocationSmallIcon';
import StoreSmallIcon from '../icons/StoreSmallIcon';
import UpIcon from '../icons/UpIcon';
import {openGoogleMaps} from '../../checkout/Checkout.Infomation';
import KPISellSkeleton from './KPISellSkeleton';
import Color from '../../../components/theme/Color';
import {sortByDateAscending} from '../../../services/util';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';

const KPIMonth = ({isBranchManager = false}) => {
  const {navigate} = useNavigation();
  const {view, isStaff} = useSelector(state => state.account);
  const isFocused = useIsFocused();
  const [isExpand, setIsExpand] = useState(false);
  const profile = useSelector(AccountSelectors.profile);
  const [data, setData] = useState([]);
  const [kpi, setKpi] = useState(undefined);
  const [loading, setLoading] = useState(true);

  const dispatch = useDispatch();

  useEffect(() => {
    if (isFocused) {
      dispatch(
        AppActions.fetch({
          onFail: rs => {
            console.log(rs.response.data);
          },
          onSuccess: rs => {
            setKpi(rs);
            const sortedItems = sortByDateAscending(rs?.remaining_pos || []);
            setData(sortedItems);
            setTimeout(() => {
              setLoading(false);
            }, 300);
          },
          url: 'plan/kpi/monthly',
          params: {
            month: moment().format('YYYY-MM'),
          },
        }),
      );
    }
  }, [isFocused]);

  const calculatePercentage = () => {
    return kpi?.in_plan && kpi?.total_plan > 0
      ? ((kpi?.in_plan / kpi?.total_plan) * 100).toFixed(0)
      : 0;
  };

  if (loading) {
    return <KPISellSkeleton />;
  }

  return (
    <View
      style={{
        marginHorizontal: 10,
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 15,
      }}>
      <View
        style={{
          backgroundColor: 'white',
          padding: 10,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
          }}>
          <View>
            <Sys.Text
              style={{
                lineHeight: 16,
                fontWeight: '600',
                marginBottom: 10,
              }}>
              KPI ghé thăm ĐBH tháng {moment().format('MM')}
            </Sys.Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
            }}>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                color: '#90BB3B',
                width: 26,
                marginBottom: 5,
              }}>
              {kpi?.un_plan + kpi?.in_plan}
            </Sys.Text>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                color: '#848484',
                marginBottom: 5,
              }}>
              điểm đã ghé thăm
            </Sys.Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
            }}>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                color: '#EF7721',
                width: 26,
                marginBottom: 5,
              }}>
              {kpi?.un_plan}
            </Sys.Text>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                color: '#848484',
              }}>
              điểm ngoài kế hoạch
            </Sys.Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
            }}>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                fontWeight: '600',
                color: '#0062FF',
                width: 26,
              }}>
              {kpi?.total_plan - kpi?.in_plan}
            </Sys.Text>
            <Sys.Text
              style={{
                fontSize: 12,
                lineHeight: 16,
                color: '#848484',
              }}>
              điểm cần đi
            </Sys.Text>
          </View>
        </View>
        <View
          style={{
            flex: 1,
            height: 100,
            alignItems: 'center',
          }}>
          <View
            style={{
              position: 'absolute',
              top: -30,
            }}>
            <VictoryPie
              cornerRadius={25}
              width={200} // Đặt chiều rộng của biểu đồ
              height={200} // Đặt chiều cao của biểu đồ
              data={[
                {x: 1, y: 1}, // Phần dữ liệu chính
                {x: 2, y: 0}, // Đặt thành 0 để không hiển thị
              ]}
              colorScale={['#E2E2EA', 'transparent']} // Màu xám cho phần nhỏ
              innerRadius={65}
              startAngle={-100} // Bắt đầu từ 90 độ, nơi phần xanh kết thúc
              endAngle={100} // Kết thúc cùng một điểm, tạo bo tròn
              style={{
                labels: {fill: 'none'},
              }}
            />
          </View>
          <View
            style={{
              position: 'absolute',
              top: -30,
            }}>
            <VictoryPie
              cornerRadius={25}
              width={200} // Đặt chiều rộng của biểu đồ
              height={200} // Đặt chiều cao của biểu đồ
              data={[
                {x: 1, y: kpi?.in_plan / kpi?.total_plan}, // Phần dữ liệu chính
                {x: 2, y: 1 - kpi?.in_plan / kpi?.total_plan}, // Đặt thành 0 để không hiển thị
              ]}
              colorScale={['#0062FF', '#E2E2EA']} // Màu xám cho phần nhỏ
              innerRadius={65}
              startAngle={-100} // Bắt đầu từ 90 độ, nơi phần xanh kết thúc
              endAngle={100} // Kết thúc cùng một điểm, tạo bo tròn
              style={{
                labels: {fill: 'none'},
              }}
            />
          </View>
          <View
            style={{
              position: 'absolute',
              bottom: 24,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Sys.Text
              style={{
                color: '#0062FF',
                fontSize: 18,
              }}>
              {calculatePercentage() && `${calculatePercentage()}%`}
            </Sys.Text>
          </View>
          <View
            style={{
              position: 'absolute',
              bottom: 0,
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%',
              paddingHorizontal: 10,
              flexDirection: 'row',
            }}>
            <View style={{}}>
              <Sys.Text
                style={{
                  fontSize: 12,
                  lineHeight: 16,
                  color: '#848484',
                }}>
                0 ĐBH
              </Sys.Text>
            </View>
            <View style={{}}>
              <Sys.Text
                style={{
                  fontSize: 12,
                  lineHeight: 16,
                  color: '#0062FF',
                }}>
                {kpi?.total_plan} ĐBH
              </Sys.Text>
            </View>
          </View>
        </View>
      </View>
      {data.length > 0 && (
        <View>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => setIsExpand(!isExpand)}
            style={{
              backgroundColor: isExpand ? '#0062FF' : '#EF7721',
              padding: 8,
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'row',
            }}>
            <Sys.Text
              style={{
                color: 'white',
                lineHeight: 16,
                fontWeight: '600',
              }}>
              Danh sách ĐBH cần đi{' '}
            </Sys.Text>
            {isStaff && data[0]?.plan?.plan_status_id && isExpand && (
              <Sys.Text
                style={{
                  color:
                    data[0].plan.plan_status_id === 1
                      ? Color.white
                      : data[0].plan.plan_status_id === 2
                      ? Color.yellow
                      : Color.green,
                  lineHeight: 16,
                  fontWeight: '600',
                }}>
                {`(${data[0]?.plan?.plan_status_label})`}
              </Sys.Text>
            )}

            {isExpand ? <UpIcon /> : <DownIcon />}
          </TouchableOpacity>
          {isExpand ? (
            <View
              style={{
                height: 300,
                backgroundColor: '#E7E7E7',
              }}>
              <FlatList
                nestedScrollEnabled
                showsVerticalScrollIndicator={false}
                data={data}
                renderItem={({item, index}) => {
                  return (
                    <TouchableOpacity
                      onPress={() => {
                        if (item) {
                          navigate('SaleInformationScreen', {
                            ...item,
                          });
                        }
                      }}
                      style={{
                        backgroundColor: '#FFFFFF99',
                        marginBottom: 5,
                        padding: 10,
                        flexDirection: 'row',
                        marginHorizontal: 10,
                      }}>
                      <View
                        style={{
                          backgroundColor: '#0062FF',
                          width: 48,
                          height: 48,
                          borderRadius: 50,
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}>
                        <View>
                          <Sys.Text
                            style={{
                              color: 'white',
                              fontSize: 12,
                              fontWeight: '700',
                              lineHeight: 15,
                            }}>
                            {item?.plan?.date
                              ? moment(item.plan.date, 'YYYY-MM-DD').format(
                                  'DD',
                                )
                              : ''}
                          </Sys.Text>
                        </View>
                        <View>
                          <Sys.Text
                            style={{
                              color: 'white',
                              fontSize: 8,
                              fontWeight: '700',
                              lineHeight: 12,
                            }}>
                            {item?.plan?.date
                              ? moment(item.plan.date, 'YYYY-MM-DD').format(
                                  'MM-YYYY',
                                )
                              : ''}
                          </Sys.Text>
                        </View>
                      </View>
                      <View
                        style={{
                          marginLeft: 15,
                          flex: 1,
                        }}>
                        <View>
                          <Sys.Text
                            style={{
                              color: '#0062FF',
                              fontWeight: '700',
                              lineHeight: 16,
                            }}>
                            {item?.posCode}{' '}
                            {item?.staff && (
                              <Sys.Text
                                style={{
                                  color: '#848484',
                                  fontWeight: '400',
                                }}>
                                ({item?.staff?.name})
                              </Sys.Text>
                            )}
                          </Sys.Text>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            marginTop: 4,
                            alignItems: 'flex-start',
                          }}>
                          <LocationSmallIcon />
                          <View style={{flex: 1, marginLeft: 4}}>
                            <Sys.Text
                              style={{
                                lineHeight: 16,
                                fontSize: 12,
                              }}>
                              {item?.address}{' '}
                            </Sys.Text>
                            <TouchableOpacity
                              onPress={() => {
                                openGoogleMaps(item?.lat, item?.lon);
                              }}>
                              <Sys.Text
                                style={{
                                  fontSize: 12,
                                  color: '#0062FF',
                                  marginTop: 2,
                                  marginLeft: 4,
                                }}>
                                (Chỉ đường)
                              </Sys.Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            marginTop: 4,
                          }}>
                          <StoreSmallIcon />
                          <Sys.Text
                            style={{
                              lineHeight: 16,
                              fontSize: 12,
                              marginLeft: 4,
                              flex: 1,
                            }}>
                            {item?.agency?.name}
                          </Sys.Text>
                        </View>
                      </View>
                    </TouchableOpacity>
                  );
                }}
                keyExtractor={(item, index) => `${index}-one-record`}
              />
            </View>
          ) : (
            <></>
          )}
        </View>
      )}
    </View>
  );
};

export default KPIMonth;
