import {useNavigation, useRoute} from '@react-navigation/native';
import moment from 'moment';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {
  Animated,
  FlatList,
  RefreshControl,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {Calendar} from 'react-native-calendars';
import MapView, {Marker, PROVIDER_GOOGLE} from 'react-native-maps';
import ReactNativeModal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import {useDispatch, useSelector} from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {AppSelectors} from '../../app.slice';
import Sys from '../../components/Sys';
import THEME from '../../components/theme/theme';
import {openGoogleMaps} from '../checkout/Checkout.Infomation';
import {PosActions, PosSelectors} from './services/pos.slice';
import {AccountSelectors} from '../account/services/account.slice';
import SaleLocationSkeleton from './SaleLocationSkeleton';
import {
  formatDistanceKm,
  formatMoney,
  getColorByDaysDifference,
  getColorByThreshold,
  isIOS,
} from '../../services/util';
import MyMarker from './icons/MyMarker';
import StandardCustomMarker from './icons/StandardCustomMarker';
import Color from '../../components/theme/Color';
import _ from 'lodash';
import CustomMarker from './icons/CustomMarker';

const SaleLocationScreen = () => {
  const {params} = useRoute();
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const [currentTab, setCurrentTab] = useState(params?.tab || 0);
  const {location, extras} = useSelector(AppSelectors.current_location);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [currentSubTab, setCurrentSubTab] = useState(params?.subTab || null);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedPos, setSelectedPos] = useState(null);
  const body = useSelector(PosSelectors.body);
  const {lastPage, total} = useSelector(state => state.pos);
  const posList = useSelector(PosSelectors.posList);
  const avatar = useSelector(AccountSelectors.avatar);
  const {view, isStaff, isGuest, isPos, isBranchOwner, isBranchManager} =
    useSelector(state => state.account);

  // Animation states
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(30));

  useEffect(() => {
    // Animate entrance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();

    // stopWatchingLocation();
    // getCurrentLocation({
    //   highAccuracy: false,
    //   setLocation,
    // });
    // return () => {
    //   dispatch(PosActions.resetBody());
    //   stopWatchingLocation();
    // };
  }, [fadeAnim, slideAnim]);

  useEffect(() => {
    if (!loading) {
      setLoading(true);
      fetchPosList();
    }
  }, [body]);

  const getPosResetData = item => {
    dispatch(
      PosActions.setPos(
        posList.map(x => {
          if (item?.posCode === x?.posCode) {
            return {
              ...x,
              allowAddPlan: false,
            };
          } else {
            return x;
          }
        }),
      ),
    );
  };

  const fetchPosList = useCallback(() => {
    dispatch(
      PosActions.getPosListByPage({
        body,
        onSuccess: response => {
          dispatch(PosActions.setTotal(response?.meta?.total));
          dispatch(PosActions.setLastPage(response?.meta?.last_page));
          dispatch(PosActions.setPos([...posList, ...response.data]));
          setLoading(false);
        },
        onFail: () => {
          setLoading(false);
        },
      }),
    );
  }, [body]);

  const onLoadMore = () => {
    if (!loading && body.page < lastPage) {
      dispatch(
        PosActions.setBody({
          page: body.page + 1,
        }),
      );
    }
  };

  const onResetData = () => {
    dispatch(PosActions.setTotal(0));
    dispatch(PosActions.setPos([]));
    dispatch(PosActions.resetBody());
  };

  const tabList = [
    {
      name: 'Hạn mức',
      icon: 'credit-card',
      gradient: ['#6366F1', '#4F46E5'],
    },
    {
      name: 'Doanh thu',
      icon: 'chart-line',
      gradient: ['#10B981', '#059669'],
    },
    {
      name: 'Ghé thăm',
      icon: 'map-marker-check',
      gradient: ['#F59E0B', '#D97706'],
    },
  ];

  // Add refresh functionality
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    dispatch(PosActions.setTotal(0));
    dispatch(PosActions.setPos([]));
    dispatch(PosActions.resetBody());
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, [dispatch]);

  const renderTab = useMemo(() => {
    return tabList.map((tab, index) => {
      const isSelected = index === currentSubTab;
      return (
        <TouchableOpacity
          key={`tab_${index.toString()}`}
          style={modernStyles.tabItem}
          activeOpacity={0.8}
          onPress={() => setCurrentSubTab(index)}>
          {isSelected ? (
            <LinearGradient
              colors={tab.gradient}
              style={modernStyles.selectedTabGradient}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 1}}>
              <MaterialCommunityIcons
                name={tab.icon}
                size={16}
                color="white"
                style={modernStyles.tabIcon}
              />
              <Sys.Text style={modernStyles.selectedTabText}>
                {tab.name}
              </Sys.Text>
            </LinearGradient>
          ) : (
            <View style={modernStyles.unselectedTab}>
              <MaterialCommunityIcons
                name={tab.icon}
                size={16}
                color={THEME.Color.gray}
                style={modernStyles.tabIcon}
              />
              <Sys.Text style={modernStyles.unselectedTabText}>
                {tab.name}
              </Sys.Text>
            </View>
          )}
        </TouchableOpacity>
      );
    });
  }, [currentSubTab, tabList]);

  const openModal = (pos, data) => {
    setSelectedPos({...pos, metaData: data});
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
    setSelectedPos(null);
  };

  const renderItem = useCallback(
    ({item, index}) => {
      return (
        <OneItem
          item={item}
          index={index}
          getPosResetData={getPosResetData}
          openModal={openModal}
          isBranchOwner={isBranchOwner}
        />
      );
    },
    [getPosResetData, openModal],
  );

  const uniqueByLatLon = posList => {
    const uniqueCoords = new Set();
    return posList.filter(pos => {
      const coord = `${pos.lat}-${pos.lon}`;
      if (!uniqueCoords.has(coord)) {
        uniqueCoords.add(coord);
        return true;
      }
      return false;
    });
  };

  const renderFooter = () => {
    if (loading) {
      return body.page === 1 ? (
        Array(6)
          .fill(null)
          .map((_, i) => <SaleLocationSkeleton key={i} />)
      ) : (
        <SaleLocationSkeleton />
      );
    }
    return null;
  };

  return (
    <Sys.Container hasHeader backgroundColor="#f8f9fa">
      <Sys.Header
        title={'Danh sách ĐBHs'}
        onBack={() => {
          navigation.goBack();
          dispatch(PosActions.setTotal(0));
          dispatch(PosActions.setPos([]));
        }}
        right={
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('SaleLocationFilterScreen', {
                onAccept: (newBody, type) => {
                  if (type === 'filter') {
                    dispatch(PosActions.setTotal(0));
                    dispatch(PosActions.setPos([]));
                    dispatch(PosActions.setBody(newBody));
                  }
                },
              });
            }}
            style={modernStyles.filterButton}>
            <LinearGradient
              colors={['#6366F1', '#4F46E5']}
              style={modernStyles.filterGradient}>
              <MaterialCommunityIcons
                name="filter-variant"
                size={20}
                color="white"
              />
            </LinearGradient>
          </TouchableOpacity>
        }
      />

      {/* Modern Header Section */}
      <Animated.View
        style={[
          modernStyles.headerSection,
          {
            opacity: fadeAnim,
            transform: [{translateY: slideAnim}],
          },
        ]}>
        <LinearGradient
          colors={['#ffffff', '#f8f9fa']}
          style={modernStyles.headerGradient}>
          <View style={modernStyles.headerContent}>
            <View style={modernStyles.titleContainer}>
              <MaterialCommunityIcons
                name="store-marker"
                size={24}
                color={THEME.Color.primary}
              />
              <View style={modernStyles.titleTextContainer}>
                <Sys.Text style={modernStyles.headerTitle}>
                  Điểm bán hàng
                </Sys.Text>
                <Sys.Text style={modernStyles.headerSubtitle}>
                  {total || 0} địa điểm
                </Sys.Text>
              </View>
            </View>

            {!loading && (
              <View style={modernStyles.viewToggle}>
                <TouchableOpacity
                  style={[
                    modernStyles.toggleButton,
                    currentTab === 0 && modernStyles.activeToggleButton,
                  ]}
                  onPress={() => setCurrentTab(0)}>
                  <MaterialCommunityIcons
                    name="view-list"
                    size={20}
                    color={currentTab === 0 ? 'white' : THEME.Color.gray}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    modernStyles.toggleButton,
                    currentTab === 1 && modernStyles.activeToggleButton,
                  ]}
                  onPress={() => setCurrentTab(1)}>
                  <MaterialCommunityIcons
                    name="map"
                    size={20}
                    color={currentTab === 1 ? 'white' : THEME.Color.gray}
                  />
                </TouchableOpacity>
              </View>
            )}
          </View>
        </LinearGradient>
      </Animated.View>
      {/* List View */}
      {currentTab === 0 && (
        <Animated.View
          style={[
            modernStyles.listContainer,
            {
              opacity: fadeAnim,
            },
          ]}>
          <FlatList
            ListFooterComponent={renderFooter}
            onEndReachedThreshold={0.2}
            onEndReached={onLoadMore}
            showsVerticalScrollIndicator={false}
            data={posList}
            keyExtractor={(item, index) =>
              `${item.posCode.toString()}-${index}`
            }
            renderItem={renderItem}
            contentContainerStyle={modernStyles.listContentContainer}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[THEME.Color.primary]}
                tintColor={THEME.Color.primary}
              />
            }
          />
        </Animated.View>
      )}

      {/* Map View */}
      {currentTab === 1 && (
        <Animated.View
          style={[
            modernStyles.mapContainer,
            {
              opacity: fadeAnim,
            },
          ]}>
          {/* Modern Tab Selector */}
          <View style={modernStyles.mapTabContainer}>
            <LinearGradient
              colors={['#ffffff', '#f8f9fa']}
              style={modernStyles.mapTabGradient}>
              <View style={modernStyles.mapTabContent}>{renderTab}</View>
            </LinearGradient>
          </View>

          <MapView
            provider={PROVIDER_GOOGLE}
            style={modernStyles.mapView}
            initialRegion={{
              latitude: 21.028511,
              longitude: 105.804817,
              latitudeDelta: 0.3,
              longitudeDelta: 0.3,
            }}
            region={
              location?.latitude
                ? {
                    latitude: location.latitude,
                    longitude: location.longitude,
                    latitudeDelta: 0.002,
                    longitudeDelta: 0.002,
                  }
                : undefined
            }>
            {location?.latitude !== 0 && location?.longitude !== 0 && (
              <Marker
                coordinate={{
                  latitude: location.latitude,
                  longitude: location.longitude,
                }}
                anchor={{x: 0.5, y: 0.5}}>
                <MyMarker avatarUri={avatar} />
              </Marker>
            )}

            {uniqueByLatLon(posList).map((x, index) => (
              <OneMarker
                key={`oneMarker-${x.posCode}-${index}`}
                index={index}
                x={x}
                tab={currentSubTab}
                openModal={openModal}
              />
            ))}
          </MapView>
        </Animated.View>
      )}
      {selectedPos && (
        <SaleLocationModal
          isVisible={modalVisible}
          pos={selectedPos}
          onClose={closeModal}
        />
      )}
    </Sys.Container>
  );
};

// Modern Styles
const modernStyles = StyleSheet.create({
  filterButton: {
    marginRight: 16,
  },
  filterGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  headerSection: {
    marginHorizontal: 12,
    marginVertical: 8,
  },
  headerGradient: {
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  titleTextContainer: {
    marginLeft: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 14,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginTop: 2,
  },
  viewToggle: {
    flexDirection: 'row',
    backgroundColor: '#f1f5f9',
    borderRadius: 12,
    padding: 4,
  },
  toggleButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginHorizontal: 2,
  },
  activeToggleButton: {
    backgroundColor: THEME.Color.primary,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  listContainer: {
    flex: 1,
  },
  listContentContainer: {
    paddingBottom: 50,
    paddingHorizontal: 4,
  },
  mapContainer: {
    flex: 1,
  },
  mapTabContainer: {
    marginHorizontal: 12,
    marginBottom: 8,
  },
  mapTabGradient: {
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  mapTabContent: {
    flexDirection: 'row',
    padding: 8,
  },
  tabItem: {
    flex: 1,
    marginHorizontal: 4,
  },
  selectedTabGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  unselectedTab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: 'transparent',
  },
  tabIcon: {
    marginRight: 6,
  },
  selectedTabText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
  },
  unselectedTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
  },
  mapView: {
    flex: 1,
    borderRadius: 16,
    marginHorizontal: 12,
    overflow: 'hidden',
  },
});

// Item Styles
const itemStyles = StyleSheet.create({
  container: {
    marginHorizontal: 12,
    marginVertical: 6,
  },
  touchable: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  gradient: {
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  addPlanButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    zIndex: 10,
  },
  addPlanGradient: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  content: {
    flexDirection: 'row',
  },
  statusContainer: {
    alignItems: 'center',
    marginRight: 16,
  },
  statusBadge: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  statusValue: {
    fontSize: 12,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  statusUnit: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
  },
  lastVisitContainer: {
    marginTop: 8,
    alignItems: 'center',
  },
  lastVisitLabel: {
    fontSize: 10,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
  },
  lastVisitDate: {
    fontSize: 10,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginTop: 2,
  },
  mainContent: {
    flex: 1,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  titleIcon: {
    marginRight: 8,
  },
  posCode: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.Color.primary,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    flex: 1,
  },
  staffName: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  infoIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  infoContent: {
    flex: 1,
  },
  addressText: {
    fontSize: 13,
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    lineHeight: 18,
  },
  directionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  directionText: {
    fontSize: 12,
    color: THEME.Color.primary,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginLeft: 4,
  },
  agencyText: {
    fontSize: 13,
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    flex: 1,
  },
});

// Enhanced Item Styles
const enhancedItemStyles = StyleSheet.create({
  container: {
    marginHorizontal: 12,
    marginVertical: 8,
  },
  touchable: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  gradient: {
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
    position: 'relative',
  },
  priorityIndicator: {
    position: 'absolute',
    top: 12,
    left: 12,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 5,
  },
  addPlanButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    zIndex: 10,
  },
  addPlanGradient: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 4,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  statusContainer: {
    alignItems: 'center',
    marginRight: 20,
  },
  statusBadge: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 4,
    position: 'relative',
  },
  statusIcon: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  statusValue: {
    fontSize: 16,
    fontWeight: '800',
    color: 'white',
    textAlign: 'center',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginTop: 4,
  },
  statusSubtext: {
    fontSize: 9,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    textAlign: 'center',
  },
  lastVisitContainer: {
    marginTop: 8,
    alignItems: 'center',
  },
  lastVisitDate: {
    fontSize: 11,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    backgroundColor: 'rgba(0,0,0,0.05)',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  mainContent: {
    flex: 1,
  },
  headerSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  titleIcon: {
    marginRight: 10,
  },
  titleTextContainer: {
    flex: 1,
  },
  posCode: {
    fontSize: 18,
    fontWeight: '700',
    color: THEME.Color.primary,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    letterSpacing: 0.3,
  },
  staffName: {
    fontSize: 13,
    fontWeight: '500',
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginTop: 2,
  },
  badgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  miniTag: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 6,
  },
  revenueTag: {
    backgroundColor: '#10B981',
  },
  distanceTag: {
    backgroundColor: '#3B82F6',
  },
  infoSection: {
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  infoIcon: {
    marginRight: 10,
    marginTop: 2,
  },
  infoContent: {
    flex: 1,
  },
  addressText: {
    fontSize: 14,
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    lineHeight: 20,
  },
  agencyText: {
    fontSize: 14,
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    flex: 1,
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.08)',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  actionText: {
    fontSize: 12,
    color: THEME.Color.primary,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginLeft: 6,
  },
  revenueInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
  },
  revenueText: {
    fontSize: 12,
    color: '#10B981',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginLeft: 4,
  },
  arrowContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SaleLocationScreen;

const OneMarker = React.memo(
  ({x, tab, openModal, index}) => {
    const settings = useSelector(AppSelectors.settings);
    const threshold_amount = settings?.pos_warn?.threshold_amount;

    const data = useMemo(() => {
      switch (tab) {
        case 0:
          return {
            label: 'Hạn mức đầu ngày',
            value: x.start_threshold_amount
              ? `${formatMoney(x.start_threshold_amount)}`
              : '0',
            color: getColorByThreshold(
              x.start_threshold_amount,
              threshold_amount,
            ),
          };

        case 1:
          return {
            label: 'Doanh thu 3 ngày gần nhất',
            value: x.recent_revenue ? `${formatMoney(x.recent_revenue)}` : '0',
            color: getColorByThreshold(x.recent_revenue, 0),
          };

        case null:
        case 2:
          const {value, color} = getColorByDaysDifference(x?.last_checkin);
          return {
            label: 'Ghé thăm gần nhất',
            value: value ? `${value} ngày` : 'chưa ghé thăm',
            color,
          };
        default:
          return {label: '---', value: '---', color: 'gray'};
      }
    }, [x, tab, threshold_amount]);

    const handlePress = () => {
      openModal(x, data);
    };

    const MarkerComponent = _.isNull(tab) ? (
      <StandardCustomMarker
        key={`marker-${x.posCode}-${index}`}
        pinColor={data.color}
        onPress={handlePress}
        amount={data.value}
        coordinate={{latitude: x?.lat, longitude: x?.lon}}
      />
    ) : (
      <CustomMarker
        key={`marker-${x.posCode}-${index}`}
        pinColor={data.color}
        onPress={handlePress}
        amount={data.value}
        coordinate={{latitude: x?.lat, longitude: x?.lon}}
      />
    );

    return MarkerComponent;
  },
  (prevProps, nextProps) => {
    return (
      prevProps.x === nextProps.x &&
      prevProps.tab === nextProps.tab &&
      prevProps.index === nextProps.index &&
      prevProps.openModal === nextProps.openModal
    );
  },
);

const OneItem = React.memo(
  ({isBranchOwner, item, index, getPosResetData, openModal}) => {
    const {navigate} = useNavigation();
    const [isShow, setIsShow] = useState(false);
    const [itemScale] = useState(new Animated.Value(1));
    const [badgeScale] = useState(new Animated.Value(1));
    const dispatch = useDispatch();

    const handlePressIn = () => {
      Animated.parallel([
        Animated.spring(itemScale, {
          toValue: 0.98,
          useNativeDriver: true,
        }),
        Animated.spring(badgeScale, {
          toValue: 1.05,
          useNativeDriver: true,
        }),
      ]).start();
    };

    const handlePressOut = () => {
      Animated.parallel([
        Animated.spring(itemScale, {
          toValue: 1,
          useNativeDriver: true,
        }),
        Animated.spring(badgeScale, {
          toValue: 1,
          useNativeDriver: true,
        }),
      ]).start();
    };

    const onCreatePlan = date => {
      dispatch(
        PosActions.planCreate({
          onSuccess: rs => {
            getPosResetData(item);
            Toast.show({
              type: 'success',
              text1: 'Thông báo',
              text2: 'Thêm kế hoạch thành công',
            });
            setIsShow(false);
          },
          onFail: rs => {
            setIsShow(false);
            Toast.show({
              type: 'error',
              text1: 'Thông báo',
              text2: rs?.error?.message,
            });
          },
          posCode: item?.posCode,
          date,
        }),
      );
    };

    const {value, color} = getColorByDaysDifference(item?.last_checkin);

    // Calculate additional metrics
    const hasRevenue = item?.recent_revenue && item?.recent_revenue > 0;
    const hasDistance = item?.distance && item?.distance > 0;
    const isHighPriority = value > 7; // More than 7 days since last visit

    // Get status info
    const getStatusInfo = () => {
      if (!item?.last_checkin) {
        return {
          icon: 'alert-circle',
          text: 'Chưa ghé thăm',
          subtext: 'Cần kiểm tra',
          priority: 'high'
        };
      }

      if (value <= 3) {
        return {
          icon: 'check-circle',
          text: `${value}`,
          subtext: 'ngày trước',
          priority: 'low'
        };
      } else if (value <= 7) {
        return {
          icon: 'clock',
          text: `${value}`,
          subtext: 'ngày trước',
          priority: 'medium'
        };
      } else {
        return {
          icon: 'alert-triangle',
          text: `${value}`,
          subtext: 'ngày trước',
          priority: 'high'
        };
      }
    };

    const statusInfo = getStatusInfo();

    return (
      <>
        <Animated.View
          style={[
            enhancedItemStyles.container,
            {
              transform: [{scale: itemScale}],
            },
          ]}>
          <TouchableOpacity
            onPress={() => {
              navigate('SaleInformationScreen', {...item});
            }}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            activeOpacity={0.9}
            style={enhancedItemStyles.touchable}>
            <LinearGradient
              colors={['#ffffff', '#f8f9fa']}
              style={enhancedItemStyles.gradient}>

              {/* Priority Indicator */}
              {isHighPriority && (
                <View style={enhancedItemStyles.priorityIndicator}>
                  <MaterialCommunityIcons
                    name="alert"
                    size={12}
                    color="#EF4444"
                  />
                </View>
              )}

              {/* Add Plan Button */}
              {item?.allowAddPlan && (
                <TouchableOpacity
                  onPress={() => setIsShow(true)}
                  style={enhancedItemStyles.addPlanButton}>
                  <LinearGradient
                    colors={['#10B981', '#059669']}
                    style={enhancedItemStyles.addPlanGradient}>
                    <MaterialCommunityIcons
                      name="calendar-plus"
                      size={16}
                      color="white"
                    />
                  </LinearGradient>
                </TouchableOpacity>
              )}

              <View style={enhancedItemStyles.content}>
                {/* Enhanced Status Badge */}
                <Animated.View
                  style={[
                    enhancedItemStyles.statusContainer,
                    { transform: [{ scale: badgeScale }] }
                  ]}>
                  <LinearGradient
                    colors={[color, color]}
                    style={enhancedItemStyles.statusBadge}>
                    <MaterialCommunityIcons
                      name={statusInfo.icon}
                      size={16}
                      color="white"
                      style={enhancedItemStyles.statusIcon}
                    />
                    <Sys.Text style={enhancedItemStyles.statusValue}>
                      {statusInfo.text}
                    </Sys.Text>
                    <Sys.Text style={enhancedItemStyles.statusSubtext}>
                      {statusInfo.subtext}
                    </Sys.Text>
                  </LinearGradient>

                  {/* Last Visit Info */}
                  {item?.last_checkin && (
                    <View style={enhancedItemStyles.lastVisitContainer}>
                      <Sys.Text style={enhancedItemStyles.lastVisitDate}>
                        {moment(item?.last_checkin, 'YYYY-MM-DD HH:mm:ss').format('DD/MM/YY')}
                      </Sys.Text>
                    </View>
                  )}
                </Animated.View>

                {/* Main Content */}
                <View style={enhancedItemStyles.mainContent}>
                  {/* Header with Title and Status Badges */}
                  <View style={enhancedItemStyles.headerSection}>
                    <View style={enhancedItemStyles.titleContainer}>
                      <MaterialCommunityIcons
                        name="store"
                        size={18}
                        color={THEME.Color.primary}
                        style={enhancedItemStyles.titleIcon}
                      />
                      <View style={enhancedItemStyles.titleTextContainer}>
                        <Sys.Text style={enhancedItemStyles.posCode}>
                          {item?.posCode}
                        </Sys.Text>
                        {isBranchOwner && item?.staff && (
                          <Sys.Text style={enhancedItemStyles.staffName}>
                            {item?.staff?.name}
                          </Sys.Text>
                        )}
                      </View>
                    </View>

                    {/* Status Badges */}
                    <View style={enhancedItemStyles.badgeContainer}>
                      {hasRevenue && (
                        <View style={[enhancedItemStyles.miniTag, enhancedItemStyles.revenueTag]}>
                          <MaterialCommunityIcons name="currency-usd" size={10} color="white" />
                        </View>
                      )}
                      {hasDistance && (
                        <View style={[enhancedItemStyles.miniTag, enhancedItemStyles.distanceTag]}>
                          <MaterialCommunityIcons name="map-marker-distance" size={10} color="white" />
                        </View>
                      )}
                    </View>
                  </View>

                  {/* Info Rows */}
                  <View style={enhancedItemStyles.infoSection}>
                    {/* Address */}
                    <View style={enhancedItemStyles.infoRow}>
                      <MaterialCommunityIcons
                        name="map-marker"
                        size={14}
                        color={THEME.Color.gray}
                        style={enhancedItemStyles.infoIcon}
                      />
                      <View style={enhancedItemStyles.infoContent}>
                        <Sys.Text style={enhancedItemStyles.addressText} numberOfLines={2}>
                          {item?.address}
                        </Sys.Text>
                      </View>
                    </View>

                    {/* Agency */}
                    <View style={enhancedItemStyles.infoRow}>
                      <MaterialCommunityIcons
                        name="office-building"
                        size={14}
                        color={THEME.Color.gray}
                        style={enhancedItemStyles.infoIcon}
                      />
                      <Sys.Text style={enhancedItemStyles.agencyText} numberOfLines={1}>
                        {item?.agency?.name}
                      </Sys.Text>
                    </View>
                  </View>

                  {/* Action Row */}
                  <View style={enhancedItemStyles.actionRow}>
                    {/* Distance & Direction */}
                    <TouchableOpacity
                      onPress={() => openGoogleMaps(item?.lat, item?.lon)}
                      style={enhancedItemStyles.actionButton}>
                      <MaterialCommunityIcons
                        name="directions"
                        size={14}
                        color={THEME.Color.primary}
                      />
                      <Sys.Text style={enhancedItemStyles.actionText}>
                        {hasDistance ? formatDistanceKm(item?.distance) : 'Chỉ đường'}
                      </Sys.Text>
                    </TouchableOpacity>

                    {/* Revenue Info */}
                    {hasRevenue && (
                      <View style={enhancedItemStyles.revenueInfo}>
                        <MaterialCommunityIcons
                          name="trending-up"
                          size={14}
                          color="#10B981"
                        />
                        <Sys.Text style={enhancedItemStyles.revenueText}>
                          {formatMoney(item?.recent_revenue)}
                        </Sys.Text>
                      </View>
                    )}

                    {/* View Details Arrow */}
                    <View style={enhancedItemStyles.arrowContainer}>
                      <MaterialCommunityIcons
                        name="chevron-right"
                        size={20}
                        color={THEME.Color.gray}
                      />
                    </View>
                  </View>
                </View>
              </View>
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>

        {isShow && (
          <AddPlanModal
            isVisible={isShow}
            onClose={() => setIsShow(false)}
            onCreatePlan={onCreatePlan}
          />
        )}
      </>
    );
  },
);

const checkStatusColor = (currentDate, code) => {
  const countDate = moment().diff(moment(currentDate, 'YYYY-MM-DD'), 'days');
  const color = getColorByDaysDifference(countDate);
  console.log(currentDate, countDate, code, color);
  return color;
};

const SaleLocationModal = ({isVisible, pos, onClose}) => {
  const insets = useSafeAreaInsets();
  const {width} = useWindowDimensions();
  const {navigate} = useNavigation();

  return (
    <ReactNativeModal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      swipeDirection="down"
      style={{
        padding: 0,
        margin: 0,
        alignItems: 'center',
        justifyContent: 'center',
      }}>
      <View style={{flex: 1}} />
      <View
        style={{
          backgroundColor: 'white',
          width: width,
          borderRadius: 20,
          padding: 20,
          paddingBottom: insets.bottom + 20,
        }}>
        <Sys.Text
          style={{
            fontWeight: isIOS ? '600' : 'bold',
            color: pos?.metaData?.color || Color.black,
          }}>
          {pos?.posCode}
        </Sys.Text>
        {pos?.metaData?.label && (
          <Sys.Text style={{color: pos?.metaData?.color}}>
            {pos?.metaData?.label} : {pos?.metaData?.value}
          </Sys.Text>
        )}
        <View style={{paddingTop: 10}}>
          <Sys.Text>{pos?.agency?.name}</Sys.Text>
          <Sys.Text style={{marginTop: 10}}>{pos?.address}</Sys.Text>
          <TouchableOpacity
            onPress={() => {
              openGoogleMaps(pos?.lat, pos?.lon);
            }}>
            <Sys.Text style={{color: '#0062FF', marginTop: 10}}>
              Chỉ đường
            </Sys.Text>
          </TouchableOpacity>
          <View style={{marginTop: 20}}>
            <Sys.Button
              onPress={() => {
                onClose();
                navigate('SaleInformationScreen', {...pos});
              }}>
              Xem chi tiết
            </Sys.Button>
          </View>
        </View>
      </View>
    </ReactNativeModal>
  );
};

const AddPlanModal = ({isVisible, onClose, onCreatePlan}) => {
  const insets = useSafeAreaInsets();
  const {width} = useWindowDimensions();

  return (
    <ReactNativeModal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      swipeDirection="down"
      style={{
        padding: 0,
        margin: 0,
        alignItems: 'center',
        justifyContent: 'center',
      }}>
      <View style={{flex: 1}} />
      <View
        style={{
          backgroundColor: 'white',
          width: width,
          borderRadius: 20,
          padding: 20,
          paddingBottom: insets.bottom + 20,
        }}>
        <Sys.Text style={{fontWeight: '600'}}>Thêm vào kế hoạch</Sys.Text>
        <Calendar
          hideArrows={true}
          initialDate={moment()
            .add(1, 'M')
            .startOf('month')
            .format('YYYY-MM-DD')}
          minDate={moment().add(1, 'M').startOf('month').format('YYYY-MM-DD')}
          maxDate={moment().add(1, 'M').endOf('month').format('YYYY-MM-DD')}
          onDayPress={day => {
            onCreatePlan(day?.dateString);
          }}
        />
      </View>
    </ReactNativeModal>
  );
};
