import { useNavigation, useRoute } from "@react-navigation/native";
import moment from "moment";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { FlatList, TouchableOpacity, useWindowDimensions, View } from "react-native";
import { Calendar } from "react-native-calendars";
import MapView, { <PERSON><PERSON>, PROVIDER_GOOGLE } from "react-native-maps";
import ReactNativeModal from "react-native-modal";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import { useDispatch, useSelector } from "react-redux";
import { AppSelectors } from "../../app.slice";
import Sys from "../../components/Sys";
import { openGoogleMaps } from "../checkout/Checkout.Infomation";
import AddPlanIcon from "./icons/AddPlanIcon";
import FilterIcon from "./icons/FilterIcon";
import GridIcon from "./icons/GridIcon";
import ListIcon from "./icons/ListIcon";
import LocationSmallIcon from "./icons/LocationSmallIcon";
import StoreSmallIcon from "./icons/StoreSmallIcon";
import { PosActions, PosSelectors } from "./services/pos.slice";
import { AccountSelectors } from "../account/services/account.slice";
import SaleLocationSkeleton from "./SaleLocationSkeleton";
import {
  formatDistanceKm,
  formatMoney,
  getColorByDaysDifference,
  getColorByThreshold,
  isIOS,
} from "../../services/util";
import MyMarker from "./icons/MyMarker";
import StandardCustomMarker from "./icons/StandardCustomMarker";
import Color from "../../components/theme/Color";
import _ from "lodash";
import CustomMarker from "./icons/CustomMarker";

const SaleLocationScreen = () => {
  const { params } = useRoute();
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const [currentTab, setCurrentTab] = useState(params?.tab || 0);
  const { location, extras } = useSelector(AppSelectors.current_location);
  const [loading, setLoading] = useState(false);
  const [currentSubTab, setCurrentSubTab] = useState(params?.subTab || null);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedPos, setSelectedPos] = useState(null);
  const body = useSelector(PosSelectors.body);
  const { lastPage, total } = useSelector(state => state.pos);
  const posList = useSelector(PosSelectors.posList);
  const avatar = useSelector(AccountSelectors.avatar);
  const { view, isStaff, isGuest, isPos, isBranchOwner, isBranchManager } =
    useSelector(state => state.account);

  useEffect(() => {
    // stopWatchingLocation();
    // getCurrentLocation({
    //   highAccuracy: false,
    //   setLocation,
    // });
    // return () => {
    //   dispatch(PosActions.resetBody());
    //   stopWatchingLocation();
    // };
  }, []);

  useEffect(() => {
    if (!loading) {
      setLoading(true);
      fetchPosList();
    }
  }, [body]);

  const getPosResetData = item => {
    dispatch(
      PosActions.setPos(
        posList.map(x => {
          if (item?.posCode === x?.posCode) {
            return {
              ...x,
              allowAddPlan: false,
            };
          } else {
            return x;
          }
        }),
      ),
    );
  };

  const fetchPosList = useCallback(() => {
    dispatch(
      PosActions.getPosListByPage({
        body,
        onSuccess: response => {
          dispatch(PosActions.setTotal(response?.meta?.total));
          dispatch(PosActions.setLastPage(response?.meta?.last_page));
          dispatch(PosActions.setPos([...posList, ...response.data]));
          setLoading(false);
        },
        onFail: () => {
          setLoading(false);
        },
      }),
    );
  }, [body]);

  const onLoadMore = () => {
    if (!loading && body.page < lastPage) {
      dispatch(
        PosActions.setBody({
          page: body.page + 1,
        }),
      );
    }
  };

  const onResetData = () => {
    dispatch(PosActions.setTotal(0));
    dispatch(PosActions.setPos([]));
    dispatch(PosActions.resetBody());
  };

  const tabList = [
    {
      name: "Hạn mức",
    },
    {
      name: "Doanh thu",
    },
    {
      name: "Ghé thăm",
    },
  ];

  const renderTab = useMemo(() => {
    return tabList.map((x, index) => (
      <View
        key={`tab_${index.toString()}`}
        style={{
          flex: 1,
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: index === currentSubTab ? "#0062FF" : "white",
          borderRadius: 5,
        }}>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => setCurrentSubTab(index)}
          key={x.name}>
          <Sys.Text
            style={{
              padding: 10,
              textAlign: "center",
              color: index === currentSubTab ? "white" : "#001451",
            }}>
            {x.name}
          </Sys.Text>
        </TouchableOpacity>
      </View>
    ));
  }, [currentSubTab]);

  const openModal = (pos, data) => {
    setSelectedPos({ ...pos, metaData: data });
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
    setSelectedPos(null);
  };

  const renderItem = useCallback(
    ({ item, index }) => {
      return (
        <OneItem
          item={item}
          index={index}
          getPosResetData={getPosResetData}
          openModal={openModal}
          isBranchOwner={isBranchOwner}
        />
      );
    },
    [getPosResetData, openModal],
  );

  const uniqueByLatLon = posList => {
    const uniqueCoords = new Set();
    return posList.filter(pos => {
      const coord = `${pos.lat}-${pos.lon}`;
      if (!uniqueCoords.has(coord)) {
        uniqueCoords.add(coord);
        return true;
      }
      return false;
    });
  };

  const renderFooter = () => {
    if (loading) {
      return body.page === 1 ? (
        Array(6)
          .fill(null)
          .map((_, i) => <SaleLocationSkeleton key={i} />)
      ) : (
        <SaleLocationSkeleton />
      );
    }
    return null;
  };

  return (
    <Sys.Container hasHeader backgroundColor="white">
      <Sys.Header
        title={"Danh sách ĐBH"}
        onBack={() => {
          navigation.goBack();
          dispatch(PosActions.setTotal(0));
          dispatch(PosActions.setPos([]));
        }}
        right={
          <View>
            <TouchableOpacity
              onPress={() => {
                navigation.navigate("SaleLocationFilterScreen", {
                  onAccept: (newBody, type) => {
                    if (type === "filter") {
                      dispatch(PosActions.setTotal(0));
                      dispatch(PosActions.setPos([]));
                      dispatch(PosActions.setBody(newBody));
                    }
                  },
                });
              }}
              style={{
                paddingHorizontal: 20,
              }}>
              <FilterIcon />
            </TouchableOpacity>
          </View>
        }
      />
      <View style={{ padding: 10, flexDirection: "row", alignItems: "center" }}>
        <View style={{ flex: 1 }}>
          <Sys.Text
            style={{
              lineHeight: 26,
              fontWeight: "700",
              color: "#001451",
              textTransform: "uppercase",
            }}>
            Danh sách điểm bán hàng ({total || 0})
          </Sys.Text>
        </View>
        {!loading && (
          <>
            <TouchableOpacity
              style={{ marginRight: 10 }}
              onPress={() => setCurrentTab(1)}>
              <ListIcon color={currentTab === 0 ? "#0062FF" : "#D1D1D1"} />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => setCurrentTab(0)}>
              <GridIcon color={currentTab === 1 ? "#0062FF" : "#D1D1D1"} />
            </TouchableOpacity>
          </>
        )}
      </View> 
      {currentTab === 0 && (
        <FlatList
          ListFooterComponent={renderFooter}
          onEndReachedThreshold={0.2}
          onEndReached={onLoadMore}
          showsVerticalScrollIndicator={false}
          data={posList}
          keyExtractor={(item, index) => `${item.posCode.toString()}-${index}`}
          renderItem={renderItem}
          contentContainerStyle={{ paddingBottom: 50 }}
        />
      )}
      {currentTab === 1 && (
        <View style={{ flex: 1 }}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              padding: 2,
              borderWidth: 1,
              borderColor: "#F1F1F5",
              margin: 5,
              borderRadius: 10,
            }}>
            {renderTab}
          </View>
          <MapView
            provider={PROVIDER_GOOGLE}
            style={{ flex: 1 }}
            initialRegion={{
              latitude: 21.028511,
              longitude: 105.804817,
              latitudeDelta: 0.3,
              longitudeDelta: 0.3,
            }}
            region={
              location?.latitude
                ? {
                  latitude: location.latitude,
                  longitude: location.longitude,
                  latitudeDelta: 0.002,
                  longitudeDelta: 0.002,
                }
                : undefined
            }>
            {location?.latitude !== 0 && location?.longitude !== 0 && (
              <Marker
                coordinate={{
                  latitude: location.latitude,
                  longitude: location.longitude,
                }}
                anchor={{ x: 0.5, y: 0.5 }}>
                <MyMarker avatarUri={avatar} />
              </Marker>
            )}

            {uniqueByLatLon(posList).map((x, index) => (
              <OneMarker
                key={`oneMarker-${x.posCode}-${index}`}
                index={index}
                x={x}
                tab={currentSubTab}
                openModal={openModal}
              />
            ))}
          </MapView>
        </View>
      )}
      {selectedPos && (
        <SaleLocationModal
          isVisible={modalVisible}
          pos={selectedPos}
          onClose={closeModal}
        />
      )}
    </Sys.Container>
  );
};

export default SaleLocationScreen;

const OneMarker = React.memo(
  ({ x, tab, openModal, index }) => {
    const settings = useSelector(AppSelectors.settings);
    const threshold_amount = settings?.pos_warn?.threshold_amount;

    const data = useMemo(() => {
      switch (tab) {
        case 0:
          return {
            label: "Hạn mức đầu ngày",
            value: x.start_threshold_amount
              ? `${formatMoney(x.start_threshold_amount)}`
              : "0",
            color: getColorByThreshold(
              x.start_threshold_amount,
              threshold_amount,
            ),
          };

        case 1:
          return {
            label: "Doanh thu 3 ngày gần nhất",
            value: x.recent_revenue ? `${formatMoney(x.recent_revenue)}` : "0",
            color: getColorByThreshold(x.recent_revenue, 0),
          };

        case null:
        case 2:
          const { value, color } = getColorByDaysDifference(x?.last_checkin);
          return {
            label: "Ghé thăm gần nhất",
            value: value ? `${value} ngày` : "chưa ghé thăm",
            color,
          };
        default:
          return { label: "---", value: "---", color: "gray" };
      }
    }, [x, tab, threshold_amount]);

    const handlePress = () => {
      openModal(x, data);
    };

    const MarkerComponent = _.isNull(tab) ? (
      <StandardCustomMarker
        key={`marker-${x.posCode}-${index}`}
        pinColor={data.color}
        onPress={handlePress}
        amount={data.value}
        coordinate={{ latitude: x?.lat, longitude: x?.lon }}
      />
    ) : (
      <CustomMarker
        key={`marker-${x.posCode}-${index}`}
        pinColor={data.color}
        onPress={handlePress}
        amount={data.value}
        coordinate={{ latitude: x?.lat, longitude: x?.lon }}
      />
    );

    return MarkerComponent;
  },
  (prevProps, nextProps) => {
    return (
      prevProps.x === nextProps.x &&
      prevProps.tab === nextProps.tab &&
      prevProps.index === nextProps.index &&
      prevProps.openModal === nextProps.openModal
    );
  },
);

const OneItem = React.memo(
  ({ isBranchOwner, item, index, getPosResetData, openModal }) => {
    const { navigate } = useNavigation();
    const [isShow, setIsShow] = useState(false);
    const dispatch = useDispatch();
    const onCreatePlan = date => {
      dispatch(
        PosActions.planCreate({
          onSuccess: rs => {
            getPosResetData(item);
            Toast.show({
              type: "success",
              text1: "Thông báo",
              text2: "Thêm kế hoạch thành công",
            });
            setIsShow(false);
          },
          onFail: rs => {
            setIsShow(false);
            Toast.show({
              type: "error",
              text1: "Thông báo",
              text2: rs?.error?.message,
            });
          },
          posCode: item?.posCode,
          date,
        }),
      );
    };

    const { value, color } = getColorByDaysDifference(item?.last_checkin);

    return (
      <>
        <TouchableOpacity
          onPress={() => {
            navigate("SaleInformationScreen", { ...item });
          }}
          style={{
            marginHorizontal: 10,
            backgroundColor: index % 2 === 0 ? "#E7E7E7" : "white",
            padding: 10,
          }}>
          {item?.allowAddPlan && (
            <TouchableOpacity
              onPress={() => {
                setIsShow(true);
              }}
              style={{
                position: "absolute",
                zIndex: 99,
                right: 3,
                top: 3,
              }}>
              <AddPlanIcon />
            </TouchableOpacity>
          )}
          <View style={{ flexDirection: "row" }}>
            <View>
              <View
                style={{
                  borderRadius: 50,
                  height: 48,
                  width: 48,
                  backgroundColor: color,
                  justifyContent: "center",
                  alignItems: "center",
                }}>
                <>
                  <Sys.Text
                    style={{
                      fontWeight: "700",
                      color: "white",
                      lineHeight: 12,
                      fontSize: _.isString(value) || !value ? 8 : 12,
                      textAlign: "center",
                    }}>
                    {_.isString(value) || value ? value : "Chưa ghé thăm"}
                  </Sys.Text>
                  {_.isNumber(value) && (
                    <Sys.Text
                      style={{
                        fontSize: 8,
                        fontWeight: "700",
                        color: "white",
                        lineHeight: 12,
                      }}>
                      Ngày
                    </Sys.Text>
                  )}
                </>
              </View>
              <View style={{ marginTop: 4 }}>
                {item?.last_checkin ? (
                  <>
                    <Sys.Text
                      style={{
                        color: "#848484",
                        fontSize: 8,
                        textAlign: "center",
                      }}>
                      Gần nhất
                    </Sys.Text>
                    <Sys.Text
                      style={{
                        color: "#848484",
                        fontSize: 8,
                        textAlign: "center",
                      }}>
                      {moment(item?.last_checkin, "YYYY-MM-DD HH:mm:ss").format(
                        "DD/MM/YYYY",
                      )}
                    </Sys.Text>
                  </>
                ) : null}
              </View>
            </View>
            <View style={{ flex: 1 }}>
              <View style={{ marginLeft: 15, flex: 1 }}>
                <View>
                  <Sys.Text
                    style={{
                      color: "#0062FF",
                      fontWeight: "700",
                      lineHeight: 16,
                    }}>
                    {item?.posCode}
                    {isBranchOwner && item?.staff && ` (${item?.staff?.name})`}
                  </Sys.Text>
                </View>
                <View
                  style={{
                    flexDirection: "row",
                    marginTop: 4,
                    alignItems: "flex-start",
                  }}>
                  <LocationSmallIcon />
                  <View style={{ flex: 1, marginLeft: 4 }}>
                    <Sys.Text style={{ lineHeight: 16, fontSize: 12 }}>
                      {item?.address}
                    </Sys.Text>
                    <TouchableOpacity
                      onPress={() => {
                        openGoogleMaps(item?.lat, item?.lon);
                      }}>
                      <Sys.Text
                        style={{
                          fontSize: 12,
                          color: "#0062FF",
                          top: 2,
                          marginLeft: 4,
                        }}>
                        (
                        {item?.distance
                          ? `Khoảng cách ${formatDistanceKm(
                            item?.distance,
                          )} Chỉ đường`
                          : "Chỉ đường"}
                        )
                      </Sys.Text>
                    </TouchableOpacity>
                  </View>
                </View>
                <View style={{ flexDirection: "row", marginTop: 4 }}>
                  <StoreSmallIcon />
                  <Sys.Text
                    style={{
                      lineHeight: 16,
                      fontSize: 12,
                      marginLeft: 4,
                      flex: 1,
                    }}>
                    {item?.agency?.name}
                  </Sys.Text>
                </View>
              </View>
            </View>
          </View>
        </TouchableOpacity>
        {isShow && (
          <AddPlanModal
            isVisible={isShow}
            onClose={() => setIsShow(false)}
            onCreatePlan={onCreatePlan}
          />
        )}
      </>
    );
  },
);

const checkStatusColor = (currentDate, code) => {
  const countDate = moment().diff(moment(currentDate, "YYYY-MM-DD"), "days");
  const color = getColorByDaysDifference(countDate);
  console.log(currentDate, countDate, code, color);
  return color;
};

const SaleLocationModal = ({ isVisible, pos, onClose }) => {
  const insets = useSafeAreaInsets();
  const { width } = useWindowDimensions();
  const { navigate } = useNavigation();

  return (
    <ReactNativeModal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      swipeDirection="down"
      style={{
        padding: 0,
        margin: 0,
        alignItems: "center",
        justifyContent: "center",
      }}>
      <View style={{ flex: 1 }} />
      <View
        style={{
          backgroundColor: "white",
          width: width,
          borderRadius: 20,
          padding: 20,
          paddingBottom: insets.bottom + 20,
        }}>
        <Sys.Text
          style={{
            fontWeight: isIOS ? "600" : "bold",
            color: pos?.metaData?.color || Color.black,
          }}>
          {pos?.posCode}
        </Sys.Text>
        {pos?.metaData?.label && (
          <Sys.Text style={{ color: pos?.metaData?.color }}>
            {pos?.metaData?.label} : {pos?.metaData?.value}
          </Sys.Text>
        )}
        <View style={{ paddingTop: 10 }}>
          <Sys.Text>{pos?.agency?.name}</Sys.Text>
          <Sys.Text style={{ marginTop: 10 }}>{pos?.address}</Sys.Text>
          <TouchableOpacity
            onPress={() => {
              openGoogleMaps(pos?.lat, pos?.lon);
            }}>
            <Sys.Text style={{ color: "#0062FF", marginTop: 10 }}>
              Chỉ đường
            </Sys.Text>
          </TouchableOpacity>
          <View style={{ marginTop: 20 }}>
            <Sys.Button
              onPress={() => {
                onClose();
                navigate("SaleInformationScreen", { ...pos });
              }}>
              Xem chi tiết
            </Sys.Button>
          </View>
        </View>
      </View>
    </ReactNativeModal>
  );
};

const AddPlanModal = ({ isVisible, onClose, onCreatePlan }) => {
  const insets = useSafeAreaInsets();
  const { width } = useWindowDimensions();

  return (
    <ReactNativeModal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      swipeDirection="down"
      style={{
        padding: 0,
        margin: 0,
        alignItems: "center",
        justifyContent: "center",
      }}>
      <View style={{ flex: 1 }} />
      <View
        style={{
          backgroundColor: "white",
          width: width,
          borderRadius: 20,
          padding: 20,
          paddingBottom: insets.bottom + 20,
        }}>
        <Sys.Text style={{ fontWeight: "600" }}>Thêm vào kế hoạch</Sys.Text>
        <Calendar
          hideArrows={true}
          initialDate={moment()
            .add(1, "M")
            .startOf("month")
            .format("YYYY-MM-DD")}
          minDate={moment().add(1, "M").startOf("month").format("YYYY-MM-DD")}
          maxDate={moment().add(1, "M").endOf("month").format("YYYY-MM-DD")}
          onDayPress={day => {
            onCreatePlan(day?.dateString);
          }}
        />
      </View>
    </ReactNativeModal>
  );
};
