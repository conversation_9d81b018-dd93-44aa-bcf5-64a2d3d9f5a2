import {
  Image,
  Pressable,
  SectionList,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useEffect} from 'react';
import Sys from '../../components/Sys';
import moment from 'moment';
import RightIcon from './icons/RightIcon';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import QRIcon from './icons/QRIcon';
import ItemRightIcon from './icons/ItemRightIcon';
import {
  addNewItem,
  changeTag,
  confirmMismatch,
  removeConfirmMismatch,
  removeStatusReport,
  removeTagChange,
  reportStatus,
  setAddNewItemModalVisible,
  setCheckinAt,
  setConfirmUncheckModalVisible,
  setCurrentItem,
  setCurrentTem,
  setDetailedViewModalVisible,
  setIsConfirmMismatchModalVisible,
  setTemModalVisible,
  toggleItemCheck,
  updateScannedData,
} from './services/inventory.slice';
import DeviceListSkeletonList from './components/DeviceListSkeletonList';
import {
  INVENTORY_PHOTO_BARCODE_REQUIRED,
  INVENTORY_PHOTO_REQUIRED,
  INVENTORY_QUANTITY_REQUIRED,
} from './services/const';
import ConfirmMismatchModal from './components/ConfirmMismatchModal';
import ConfirmUncheckModal from './components/ConfirmUncheckModal';
import DetailedViewModal from './components/DetailedViewModal';
import AddNewItemModal from './components/AddNewItemModal';
import {handleModalDetailActionMode2} from './services/utilities';
import {checkAndRequestCameraPermission, isAndroid} from '../../services/util';
import Color from '../../components/theme/Color';

const DeviceListScreen = () => {
  const dispatch = useDispatch();
  const {
    sections,
    sectionsFull,
    sectionsHistories,
    isLoading,
    scannedData,
    currentItem,
    isConfirmMismatchModalVisible,
    isConfirmUncheckModalVisible,
    mode,
  } = useSelector(state => state.inventory);
  const {navigate, ...navigation} = useNavigation();

  const handleScanComplete = item => photo => temData => {
    const scanData = {
      item,
      photo,
      temData,
    };
    if (
      item?.inventory_requirement === INVENTORY_PHOTO_BARCODE_REQUIRED &&
      !temData
    ) {
      console.log('INVENTORY_PHOTO_BARCODE_REQUIRED BACK');
      navigate('DeviceListScreen');
      return;
    }
    if (photo) {
      dispatch(updateScannedData(scanData));
    }
    navigate('DeviceListScreen');
  };

  const handleAddNewItem = newItem => {
    dispatch(addNewItem(newItem));
  };

  useEffect(() => {
    if (currentItem && scannedData) {
      if (
        currentItem.inventory_requirement ===
          INVENTORY_PHOTO_BARCODE_REQUIRED &&
        currentItem.tem_code !== scannedData.tem_code
      ) {
        dispatch(setIsConfirmMismatchModalVisible(true));
      } else {
        handleToggleItemCheck(currentItem, true);
      }
    }
  }, [scannedData, currentItem]);

  const handleToggleItemCheck = (item, confirm) => {
    if (confirm) {
      dispatch(
        toggleItemCheck({
          itemId: item.id,
        }),
      );
    }
  };

  const handleCheckBoxPress = item => {
    if (mode === 2) {
      return;
    }
    if (
      item.checked &&
      (item.isNew ||
        item.statusData ||
        item.isMissing ||
        item.tagChanged ||
        item.inventory_requirement !== INVENTORY_QUANTITY_REQUIRED)
    ) {
      dispatch(setCurrentItem(item));
      dispatch(setConfirmUncheckModalVisible(true));
      return;
    }
    if (!item.checked) {
      if (item.inventory_requirement === INVENTORY_QUANTITY_REQUIRED) {
        dispatch(
          toggleItemCheck({
            itemId: item.id,
          }),
        );
      } else if (
        item.inventory_requirement === INVENTORY_PHOTO_REQUIRED ||
        item.inventory_requirement === INVENTORY_PHOTO_BARCODE_REQUIRED
      ) {
        dispatch(setCurrentItem(item));
        navigate('CameraScreen', {
          nextScreen:
            item.inventory_requirement === INVENTORY_PHOTO_BARCODE_REQUIRED &&
            'ScanCodeScreen',
          onCompleteCamera: handleScanComplete(item),
        });
      }
    } else {
      dispatch(
        toggleItemCheck({
          itemId: item.id,
        }),
      );
    }
  };

  const handleScanHeaderComplete = tem => {
    console.log('tem ScanCodeScreen', tem);
    if (!tem?.id) {
      navigate('DeviceListScreen');
      return;
    }
    const item = findItemInSections(tem.id);

    if (item) {
      dispatch(setCurrentItem(item));
      if (item?.isNew) {
        dispatch(
          setAddNewItemModalVisible({
            show: true,
            isEdit: true,
            typeId: item.supplier_type_id,
          }),
        );
      } else {
        dispatch(setDetailedViewModalVisible({show: true}));
      }
    } else if (tem.item) {
      dispatch(setCurrentItem({...tem.item, notExit: true}));
      dispatch(setDetailedViewModalVisible({show: true}));
    } else {
      dispatch(setCurrentTem(tem));
      dispatch(setTemModalVisible(true));
    }
    navigate('DeviceListScreen');
  };

  const findItemInSections = temId => {
    for (let section of sections) {
      for (let group of section.data) {
        let item = group.items.find(item => item.tem && item.tem.id === temId);
        if (item) {
          return item;
        }
      }
    }
    return null;
  };

  const handleModalDetailActionMode1 = useCallback(actionState => {
    if (actionState.type === 'REPORT') {
      if (actionState.action) {
        dispatch(reportStatus(actionState.data));
      } else {
        dispatch(removeStatusReport(actionState.data));
      }
    } else if (actionState.type === 'MISSING') {
      if (actionState.action) {
        dispatch(confirmMismatch(actionState.data));
      } else {
        dispatch(removeConfirmMismatch(actionState.data));
      }
    } else if (actionState.type === 'CHANGE_TAG') {
      if (actionState.action) {
        dispatch(changeTag(actionState.data));
      } else {
        dispatch(removeTagChange(actionState.data));
      }
    }
  }, []);

  useEffect(() => {
    if (mode === 4) {
      const checkin_time_at = moment().format('YYYY-MM-DD HH:mm:ss');
      dispatch(setCheckinAt(checkin_time_at));
    }
  }, []);

  useEffect(() => {
    const handleCameraPermission = async () => {
      const hasPermission = await checkAndRequestCameraPermission();
      if (!hasPermission) {
        navigation.goBack();
      }
    };

    handleCameraPermission();
  }, []);

  const renderSectionHeader = ({section: {title, data}}) => {
    return (
      <View>
        <View
          style={[
            styles.sectionHeader,
            {backgroundColor: title === 'Vietlott' ? '#D30B0D' : '#001451'},
          ]}>
          <View style={styles.sectionHeaderTextContainer}>
            <Sys.Text style={styles.sectionHeaderText}>{title}</Sys.Text>
          </View>
          <View style={styles.sectionHeaderTextQuantity}>
            {/*<Sys.Text style={styles.sectionHeaderText}>Số lượng</Sys.Text>*/}
            <Sys.Text style={styles.sectionHeaderText}>Sổ sách</Sys.Text>
          </View>
          <View style={styles.sectionHeaderTextQuantity}>
            <Sys.Text style={[styles.sectionHeaderText, {}]}>Thực tế</Sys.Text>
          </View>
        </View>
        {renderEmptySection(data)}
      </View>
    );
  };

  const renderEmptySection = data => {
    if (data.length === 0) {
      return (
        <View style={styles.emptySection}>
          <Sys.Text style={styles.emptySectionText}>
            Không có thiết bị nào
          </Sys.Text>
        </View>
      );
    }
  };

  const renderSectionFooter = ({section}) => {
    if (mode === 2 || mode === 3) {
      return null;
    }
    return (
      <TouchableOpacity
        onPress={() => {
          dispatch(
            setAddNewItemModalVisible({show: true, typeId: section.type_id}),
          );
        }}
        style={styles.addButton}>
        <Sys.Text style={styles.addButtonText}>+ Thêm thiết bị</Sys.Text>
      </TouchableOpacity>
    );
  };

  const renderItem = ({item, section}) => {
    return (
      <OneItem
        item={item}
        sectionTitle={section.title}
        navigate={navigate}
        handleCheckBoxPress={handleCheckBoxPress}
      />
    );
  };

  const allItemsChecked =
    sections?.length > 0 &&
    sections.every(
      section =>
        section?.data?.length === 0 ||
        section.data.every(
          group =>
            group?.items?.length > 0 &&
            group.items.every(item => item?.checked),
        ),
    );

  return (
    <Sys.Container hasHeader hasFooter footerColor="white">
      <Sys.Header
        title={
          mode === 1 || mode === 2 || mode === 4
            ? 'Trang thiết bị'
            : 'Lịch sử kiểm kê TTB'
        }
        right={
          mode !== 3 && (
            <TouchableOpacity
              onPress={() => {
                navigate('ScanCodeScreen', {
                  onComplete: handleScanHeaderComplete,
                });
              }}
              style={styles.headerRight}>
              <QRIcon />
            </TouchableOpacity>
          )
        }
      />
      {isLoading ? (
        <DeviceListSkeletonList />
      ) : (
        <SectionList
          sections={
            mode === 1 || mode === 4
              ? sections
              : mode === 2
              ? sectionsFull
              : sectionsHistories
          }
          keyExtractor={(item, index) => item + index}
          renderSectionHeader={renderSectionHeader}
          renderItem={renderItem}
          stickySectionHeadersEnabled={true}
          renderEmptySection={renderEmptySection}
          renderSectionFooter={renderSectionFooter}
        />
      )}
      <View style={styles.footer}>
        {(mode === 1 || mode === 4) && (
          <TouchableOpacity
            onPress={() =>
              mode === 1
                ? navigate('ConformCheckinScreen')
                : navigate('CheckinResultScreen')
            }
            disabled={!allItemsChecked}
            style={[
              styles.nextButton,
              {
                opacity: allItemsChecked ? 1 : 0.5,
              },
            ]}>
            <Sys.Text style={styles.nextButtonText}>Tiếp theo</Sys.Text>
            <View style={styles.nextButtonIcon}>
              <RightIcon color={'white'} />
            </View>
          </TouchableOpacity>
        )}

        {/* modal  khác mã tem*/}
        {currentItem && scannedData && (
          <ConfirmMismatchModal
            isVisible={isConfirmMismatchModalVisible}
            onClose={() => {
              dispatch(setIsConfirmMismatchModalVisible(false));
              handleToggleItemCheck(currentItem, false);
            }}
            onUpdate={() => {
              dispatch(setIsConfirmMismatchModalVisible(false));
              handleToggleItemCheck(currentItem, true);
            }}
            currentItem={currentItem}
            scannedData={scannedData}
          />
        )}

        <ConfirmUncheckModal
          isVisible={isConfirmUncheckModalVisible}
          onConfirm={() => {
            dispatch(setConfirmUncheckModalVisible(false));
            handleToggleItemCheck(currentItem, true);
          }}
          onClose={() => {
            dispatch(setConfirmUncheckModalVisible(false));
            handleToggleItemCheck(currentItem, false);
          }}
          item={currentItem}
        />
        <DetailedViewModal
          item={currentItem}
          onAction={
            mode === 1 || mode === 4
              ? handleModalDetailActionMode1
              : mode === 2
              ? handleModalDetailActionMode2
              : () => {}
          }
        />
        <AddNewItemModal onSave={handleAddNewItem} />
      </View>
    </Sys.Container>
  );
};

export default DeviceListScreen;

const OneItem = ({item, handleCheckBoxPress}) => (
  <View>
    <View style={styles.itemContainer}>
      <View style={styles.itemName}>
        <Sys.Text style={styles.itemNameText}>{item.typeName}</Sys.Text>
      </View>
      <View style={[styles.itemQuantity, {width: 80}]}>
        <Sys.Text style={styles.itemQuantityText}>{item.quantity}</Sys.Text>
      </View>
      <View style={styles.itemQuantity}>
        <Sys.Text style={styles.itemRealQuantityText}>
          {item.realQuantity || 0}
        </Sys.Text>
      </View>
    </View>
    {item.items.map((oneItem, index) => (
      <OneSubItem
        item={oneItem}
        key={`${index}-item-${item.typeName}`}
        handleCheckBoxPress={handleCheckBoxPress}
      />
    ))}
  </View>
);

const OneSubItem = ({item, handleCheckBoxPress}) => {
  const dispatch = useDispatch();
  const {equipmentStatus, mode} = useSelector(state => state.inventory);
  return (
    <View style={styles.subItemContainer}>
      {item.photo && (
        <Image
          source={{
            uri: mode === 1 || mode === 4 ? item.photo?.uri : item.photo,
          }}
          style={styles.subItemImage}
          resizeMode="cover"
        />
      )}
      <View style={styles.subItemDetails}>
        {item.tem_code && <Sys.Text>Mã TTB:</Sys.Text>}
        <Sys.Text>Ngày bàn giao:</Sys.Text>
        <Sys.Text>Tình trạng:</Sys.Text>
      </View>
      <View style={styles.subItemData}>
        {item.tem_code && (
          <Sys.Text style={styles.subItemDataText}>{item.tem_code}</Sys.Text>
        )}
        <Sys.Text style={styles.subItemDataText}>
          {moment(item?.handover_date).format('DD/MM/YYYY')}
        </Sys.Text>

        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'center',
            marginRight: -20,
          }}>
          {item?.status && !item.isStatus && !item.isMissing && (
            <Sys.Text
              style={[
                styles.labelText,
                styles.subItemStatus,
                {
                  color: item?.status_id === 1 ? Color.green : Color.red,
                },
              ]}>
              {item?.status}
            </Sys.Text>
          )}

          {item.isNew && (
            <Sys.Text style={[styles.labelText, {color: '#0062FF'}]}>
              {' '}
              Mới
            </Sys.Text>
          )}
          {item.isStatus && (
            <Sys.Text style={[styles.labelText, {color: 'orange'}]}>
              {(() => {
                const statusName = equipmentStatus[
                  item.statusData.status_type
                ]?.find(
                  status => status.id === item.statusData.status_id,
                )?.name;
                return statusName.length > 10
                  ? `${statusName.slice(0, 10)}...`
                  : statusName;
              })()}
            </Sys.Text>
          )}
          {item.isMissing && (
            <Sys.Text style={[styles.labelText, {color: 'red'}]}>
              {' '}
              Thiếu
            </Sys.Text>
          )}
          {item.tagChanged && (
            <Sys.Text style={[styles.labelText, {color: 'blue'}]}>
              | {item?.tagData?.oldTag ? 'Đổi tem' : 'Dán tem mới'}
            </Sys.Text>
          )}
        </View>
      </View>
      {(mode === 1 || mode === 4 || mode === 3) && (
        <Pressable
          onPress={() =>
            mode === 1 || mode === 4 ? handleCheckBoxPress(item) : () => {}
          }
          style={({pressed}) => [
            styles.checkBoxContainer,
            {
              backgroundColor: pressed
                ? 'rgba(255,255,255,0.5)'
                : 'transparent',
            },
          ]}>
          <Sys.Checkbox
            checked={item.checked}
            onPress={() =>
              mode === 1 || mode === 4 ? handleCheckBoxPress(item) : () => {}
            }
          />
        </Pressable>
      )}

      {mode === 2 && (
        <View style={[styles.checkBoxContainer, {marginRight: 15}]}>
          <Sys.Text style={[styles.itemRealQuantityText]}>
            {item?.latest_actual_qty}
          </Sys.Text>
        </View>
      )}

      <Pressable
        onPress={() => {
          dispatch(setCurrentItem(item));
          if (item?.isNew) {
            dispatch(
              setAddNewItemModalVisible({
                show: true,
                isEdit: true,
                typeId: item.supplier_type_id,
              }),
            );
          } else {
            dispatch(setDetailedViewModalVisible({show: true}));
          }
        }}
        style={({pressed}) => [
          styles.iconDetail,
          {backgroundColor: pressed ? 'rgba(255,255,255,0.5)' : 'transparent'},
        ]}>
        <ItemRightIcon />
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  label: {},
  labelText: {
    fontSize: 12,
  },
  headerRight: {
    width: 35,
    height: 35,
    backgroundColor: '#0062FF',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    marginRight: 10,
    marginBottom: isAndroid ? -1 : 10,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    paddingHorizontal: 20,
  },
  sectionHeaderTextContainer: {
    flexBasis: 'auto',
    flexGrow: 1,
    flexShrink: 0,
  },
  sectionHeaderText: {
    flexBasis: 100,
    flexShrink: 1,
    flexGrow: 0,
    color: 'white',
    fontWeight: 'bold',
  },
  sectionHeaderTextQuantity: {
    flexBasis: 85,
    flexShrink: 1,
    flexGrow: 0,
    width: 100,
    alignItems: 'center',
    fontWeight: 'bold',
  },
  addButton: {
    padding: 10,
    backgroundColor: '#0062FF',
    borderBottomEndRadius: 10,
    borderBottomLeftRadius: 10,
    alignItems: 'center',
    marginBottom: 30,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  footer: {
    backgroundColor: 'white',
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextButton: {
    padding: 10,
    backgroundColor: '#0062FF',
    borderRadius: 50,
    paddingHorizontal: 40,
    flexDirection: 'row',
    alignItems: 'center',
  },
  nextButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  nextButtonIcon: {
    position: 'absolute',
    right: 15,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalText: {
    marginBottom: 10,
    textAlign: 'center',
    fontSize: 14,
  },
  itemContainer: {
    flexDirection: 'row',
    padding: 10,
    paddingHorizontal: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#D1D1D1',
    height: 55,
    alignItems: 'center',
  },
  itemName: {
    flex: 1,
  },
  itemNameText: {
    fontWeight: '700',
  },
  itemQuantity: {
    width: 100,
  },
  itemQuantityText: {
    textAlign: 'center',
    color: '#90BB3B',
    fontSize: 16,
    fontWeight: 'bold',
  },
  itemRealQuantityText: {
    textAlign: 'center',
    color: '#FF0000',
    fontSize: 16,
    fontWeight: 'bold',
  },
  subItemContainer: {
    flexDirection: 'row',
    padding: 10,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#D1D1D1',
    alignItems: 'center',
  },
  subItemDetails: {
    flex: 1,
  },
  subItemData: {
    width: 100,
    marginRight: 10,
  },
  subItemDataText: {
    textAlign: 'right',
    color: '#848484',
  },
  subItemStatus: {
    color: '#90BB3B',
    textAlign: 'right',
  },
  iconDetail: {
    paddingVertical: 20,
    paddingLeft: 20,
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
  checkBoxContainer: {
    marginHorizontal: 30,
    marginRight: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptySection: {
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptySectionText: {
    paddingVertical: 50,
    color: '#848484',
  },
  subItemImage: {
    width: 50, // Set your desired width
    height: 50, // Set your desired height
    marginRight: 10,
    borderRadius: 5, // Optional: if you want rounded corners
  },
});
