import {
  Animated,
  Image,
  Refresh<PERSON>ontrol,
  SectionList,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Sys from '../../components/Sys';
import THEME from '../../components/theme/theme';
import moment from 'moment';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {
  addNewItem,
  changeTag,
  confirmMismatch,
  removeConfirmMismatch,
  removeStatusReport,
  removeTagChange,
  reportStatus,
  setAddNewItemModalVisible,
  setCheckinAt,
  setConfirmUncheckModalVisible,
  setCurrentItem,
  setCurrentTem,
  setDetailedViewModalVisible,
  setIsConfirmMismatchModalVisible,
  setTemModalVisible,
  toggleItemCheck,
  updateScannedData,
} from './services/inventory.slice';
import DeviceListSkeletonList from './components/DeviceListSkeletonList';
import {
  INVENTORY_PHOTO_BARCODE_REQUIRED,
  INVENTORY_PHOTO_REQUIRED,
  INVENTORY_QUANTITY_REQUIRED,
} from './services/const';
import ConfirmMismatchModal from './components/ConfirmMismatchModal';
import ConfirmUncheckModal from './components/ConfirmUncheckModal';
import DetailedViewModal from './components/DetailedViewModal';
import AddNewItemModal from './components/AddNewItemModal';
import {handleModalDetailActionMode2} from './services/utilities';
import {checkAndRequestCameraPermission, isAndroid} from '../../services/util';

const DeviceListScreen = () => {
  const dispatch = useDispatch();
  const {
    sections,
    sectionsFull,
    sectionsHistories,
    isLoading,
    scannedData,
    currentItem,
    isConfirmMismatchModalVisible,
    isConfirmUncheckModalVisible,
    mode,
  } = useSelector(state => state.inventory);
  const {navigate, ...navigation} = useNavigation();

  // Animation states
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(30));
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Initialize animations
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleScanComplete = item => photo => temData => {
    const scanData = {
      item,
      photo,
      temData,
    };
    if (
      item?.inventory_requirement === INVENTORY_PHOTO_BARCODE_REQUIRED &&
      !temData
    ) {
      console.log('INVENTORY_PHOTO_BARCODE_REQUIRED BACK');
      navigate('DeviceListScreen');
      return;
    }
    if (photo) {
      dispatch(updateScannedData(scanData));
    }
    navigate('DeviceListScreen');
  };

  const handleAddNewItem = newItem => {
    dispatch(addNewItem(newItem));
  };

  useEffect(() => {
    if (currentItem && scannedData) {
      if (
        currentItem.inventory_requirement ===
          INVENTORY_PHOTO_BARCODE_REQUIRED &&
        currentItem.tem_code !== scannedData.tem_code
      ) {
        dispatch(setIsConfirmMismatchModalVisible(true));
      } else {
        handleToggleItemCheck(currentItem, true);
      }
    }
  }, [scannedData, currentItem]);

  const handleToggleItemCheck = (item, confirm) => {
    if (confirm) {
      dispatch(
        toggleItemCheck({
          itemId: item.id,
        }),
      );
    }
  };

  const handleCheckBoxPress = item => {
    if (mode === 2) {
      return;
    }
    if (
      item.checked &&
      (item.isNew ||
        item.statusData ||
        item.isMissing ||
        item.tagChanged ||
        item.inventory_requirement !== INVENTORY_QUANTITY_REQUIRED)
    ) {
      dispatch(setCurrentItem(item));
      dispatch(setConfirmUncheckModalVisible(true));
      return;
    }
    if (!item.checked) {
      if (item.inventory_requirement === INVENTORY_QUANTITY_REQUIRED) {
        dispatch(
          toggleItemCheck({
            itemId: item.id,
          }),
        );
      } else if (
        item.inventory_requirement === INVENTORY_PHOTO_REQUIRED ||
        item.inventory_requirement === INVENTORY_PHOTO_BARCODE_REQUIRED
      ) {
        dispatch(setCurrentItem(item));
        navigate('CameraScreen', {
          nextScreen:
            item.inventory_requirement === INVENTORY_PHOTO_BARCODE_REQUIRED &&
            'ScanCodeScreen',
          onCompleteCamera: handleScanComplete(item),
        });
      }
    } else {
      dispatch(
        toggleItemCheck({
          itemId: item.id,
        }),
      );
    }
  };

  const handleScanHeaderComplete = tem => {
    console.log('tem ScanCodeScreen', tem);
    if (!tem?.id) {
      navigate('DeviceListScreen');
      return;
    }
    const item = findItemInSections(tem.id);

    if (item) {
      dispatch(setCurrentItem(item));
      if (item?.isNew) {
        dispatch(
          setAddNewItemModalVisible({
            show: true,
            isEdit: true,
            typeId: item.supplier_type_id,
          }),
        );
      } else {
        dispatch(setDetailedViewModalVisible({show: true}));
      }
    } else if (tem.item) {
      dispatch(setCurrentItem({...tem.item, notExit: true}));
      dispatch(setDetailedViewModalVisible({show: true}));
    } else {
      dispatch(setCurrentTem(tem));
      dispatch(setTemModalVisible(true));
    }
    navigate('DeviceListScreen');
  };

  const findItemInSections = temId => {
    for (let section of sections) {
      for (let group of section.data) {
        let item = group.items.find(item => item.tem && item.tem.id === temId);
        if (item) {
          return item;
        }
      }
    }
    return null;
  };

  const handleModalDetailActionMode1 = useCallback(actionState => {
    if (actionState.type === 'REPORT') {
      if (actionState.action) {
        dispatch(reportStatus(actionState.data));
      } else {
        dispatch(removeStatusReport(actionState.data));
      }
    } else if (actionState.type === 'MISSING') {
      if (actionState.action) {
        dispatch(confirmMismatch(actionState.data));
      } else {
        dispatch(removeConfirmMismatch(actionState.data));
      }
    } else if (actionState.type === 'CHANGE_TAG') {
      if (actionState.action) {
        dispatch(changeTag(actionState.data));
      } else {
        dispatch(removeTagChange(actionState.data));
      }
    }
  }, []);

  useEffect(() => {
    if (mode === 4) {
      const checkin_time_at = moment().format('YYYY-MM-DD HH:mm:ss');
      dispatch(setCheckinAt(checkin_time_at));
    }
  }, []);

  useEffect(() => {
    const handleCameraPermission = async () => {
      const hasPermission = await checkAndRequestCameraPermission();
      if (!hasPermission) {
        navigation.goBack();
      }
    };

    handleCameraPermission();
  }, []);

  const renderSectionHeader = ({section: {title, data}}) => {
    const isVietlott = title === 'Vietlott';
    const gradientColors = isVietlott
      ? ['#D30B0D', '#B91C1C']
      : ['#1E40AF', '#1D4ED8'];

    return (
      <View>
        <LinearGradient
          colors={gradientColors}
          style={modernStyles.sectionHeader}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 0}}>
          {/* Icon and Title */}
          <View style={modernStyles.sectionHeaderLeft}>
            <View style={modernStyles.sectionIconContainer}>
              <MaterialCommunityIcons
                name={isVietlott ? 'ticket' : 'devices'}
                size={20}
                color="white"
              />
            </View>
            <View style={modernStyles.sectionTitleContainer}>
              <Sys.Text style={modernStyles.sectionHeaderTitle}>
                {title}
              </Sys.Text>
              <Sys.Text style={modernStyles.sectionHeaderSubtitle}>
                {data.length} nhóm thiết bị
              </Sys.Text>
            </View>
          </View>

          {/* Quantity Headers */}
          <View style={modernStyles.sectionHeaderRight}>
            <View style={modernStyles.quantityHeader}>
              <MaterialCommunityIcons
                name="book-open"
                size={16}
                color="rgba(255,255,255,0.8)"
              />
              <Sys.Text style={modernStyles.quantityHeaderText}>
                Sổ sách
              </Sys.Text>
            </View>
            <View style={modernStyles.quantityHeader}>
              <MaterialCommunityIcons
                name="check-circle"
                size={16}
                color="rgba(255,255,255,0.8)"
              />
              <Sys.Text style={modernStyles.quantityHeaderText}>
                Thực tế
              </Sys.Text>
            </View>
          </View>
        </LinearGradient>
        {renderEmptySection(data)}
      </View>
    );
  };

  const renderEmptySection = data => {
    if (data.length === 0) {
      return (
        <View style={modernStyles.emptySection}>
          <MaterialCommunityIcons
            name="package-variant"
            size={48}
            color={THEME.Color.grayLight}
          />
          <Sys.Text style={modernStyles.emptySectionText}>
            Không có thiết bị nào
          </Sys.Text>
          <Sys.Text style={modernStyles.emptySectionSubtext}>
            Chưa có thiết bị được thêm vào danh mục này
          </Sys.Text>
        </View>
      );
    }
  };

  const renderSectionFooter = ({section}) => {
    if (mode === 2 || mode === 3) {
      return null;
    }

    return (
      <TouchableOpacity
        onPress={() => {
          dispatch(
            setAddNewItemModalVisible({show: true, typeId: section.type_id}),
          );
        }}
        style={modernStyles.addButton}
        activeOpacity={0.8}>
        <LinearGradient
          colors={['#10B981', '#059669']}
          style={modernStyles.addButtonGradient}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 0}}>
          <MaterialCommunityIcons name="plus-circle" size={20} color="white" />
          <Sys.Text style={modernStyles.addButtonText}>Thêm thiết bị</Sys.Text>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  const renderItem = ({item, section}) => {
    return (
      <OneItem
        item={item}
        sectionTitle={section.title}
        navigate={navigate}
        handleCheckBoxPress={handleCheckBoxPress}
      />
    );
  };

  const allItemsChecked =
    sections?.length > 0 &&
    sections.every(
      section =>
        section?.data?.length === 0 ||
        section.data.every(
          group =>
            group?.items?.length > 0 &&
            group.items.every(item => item?.checked),
        ),
    );

  // Add refresh functionality
  const onRefresh = useCallback(() => {
    setIsRefreshing(true);
    // Add your refresh logic here
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  }, []);

  return (
    <Sys.Container hasHeader hasFooter footerColor="white">
      <Sys.Header
        title={
          mode === 1 || mode === 2 || mode === 4
            ? 'Trang thiết bị'
            : 'Lịch sử kiểm kê TTB'
        }
        right={
          mode !== 3 && (
            <TouchableOpacity
              onPress={() => {
                navigate('ScanCodeScreen', {
                  onComplete: handleScanHeaderComplete,
                });
              }}
              style={modernStyles.headerRight}>
              <LinearGradient
                colors={['#3B82F6', '#1D4ED8']}
                style={modernStyles.headerRightGradient}>
                <MaterialCommunityIcons
                  name="qrcode-scan"
                  size={20}
                  color="white"
                />
              </LinearGradient>
            </TouchableOpacity>
          )
        }
      />

      <Animated.View
        style={[
          modernStyles.contentContainer,
          {
            opacity: fadeAnim,
            transform: [{translateY: slideAnim}],
          },
        ]}>
        {isLoading ? (
          <DeviceListSkeletonList />
        ) : (
          <SectionList
            sections={
              mode === 1 || mode === 4
                ? sections
                : mode === 2
                ? sectionsFull
                : sectionsHistories
            }
            keyExtractor={(item, index) => item + index}
            renderSectionHeader={renderSectionHeader}
            renderItem={renderItem}
            stickySectionHeadersEnabled={true}
            renderEmptySection={renderEmptySection}
            renderSectionFooter={renderSectionFooter}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={onRefresh}
                colors={[THEME.Color.primary]}
                tintColor={THEME.Color.primary}
              />
            }
            showsVerticalScrollIndicator={false}
          />
        )}
      </Animated.View>
      <View style={modernStyles.footer}>
        {(mode === 1 || mode === 4) && (
          <TouchableOpacity
            onPress={() =>
              mode === 1
                ? navigate('ConformCheckinScreen')
                : navigate('CheckinResultScreen')
            }
            disabled={!allItemsChecked}
            style={modernStyles.nextButtonContainer}
            activeOpacity={0.8}>
            <LinearGradient
              colors={
                allItemsChecked
                  ? ['#10B981', '#059669']
                  : ['#9CA3AF', '#6B7280']
              }
              style={modernStyles.nextButton}>
              <MaterialCommunityIcons
                name="check-circle"
                size={20}
                color="white"
                style={modernStyles.nextButtonIcon}
              />
              <Sys.Text style={modernStyles.nextButtonText}>
                {allItemsChecked ? 'Tiếp theo' : 'Chưa hoàn thành'}
              </Sys.Text>
              <MaterialCommunityIcons
                name="arrow-right"
                size={20}
                color="white"
              />
            </LinearGradient>
          </TouchableOpacity>
        )}

        {/* modal  khác mã tem*/}
        {currentItem && scannedData && (
          <ConfirmMismatchModal
            isVisible={isConfirmMismatchModalVisible}
            onClose={() => {
              dispatch(setIsConfirmMismatchModalVisible(false));
              handleToggleItemCheck(currentItem, false);
            }}
            onUpdate={() => {
              dispatch(setIsConfirmMismatchModalVisible(false));
              handleToggleItemCheck(currentItem, true);
            }}
            currentItem={currentItem}
            scannedData={scannedData}
          />
        )}

        <ConfirmUncheckModal
          isVisible={isConfirmUncheckModalVisible}
          onConfirm={() => {
            dispatch(setConfirmUncheckModalVisible(false));
            handleToggleItemCheck(currentItem, true);
          }}
          onClose={() => {
            dispatch(setConfirmUncheckModalVisible(false));
            handleToggleItemCheck(currentItem, false);
          }}
          item={currentItem}
        />
        <DetailedViewModal
          item={currentItem}
          onAction={
            mode === 1 || mode === 4
              ? handleModalDetailActionMode1
              : mode === 2
              ? handleModalDetailActionMode2
              : () => {}
          }
        />
        <AddNewItemModal onSave={handleAddNewItem} />
      </View>
    </Sys.Container>
  );
};

export default DeviceListScreen;

const OneItem = ({item, handleCheckBoxPress}) => {
  const [itemScale] = useState(new Animated.Value(1));

  const handlePressIn = () => {
    Animated.spring(itemScale, {
      toValue: 0.98,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(itemScale, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View
      style={[modernStyles.itemWrapper, {transform: [{scale: itemScale}]}]}>
      <LinearGradient
        colors={['#ffffff', '#f8f9fa']}
        style={modernStyles.itemContainer}>
        {/* Enhanced Item Header */}
        <LinearGradient
          colors={['#F8FAFC', '#F1F5F9']}
          style={modernStyles.itemHeader}>
          <View style={modernStyles.itemHeaderLeft}>
            <LinearGradient
              colors={['#6366F1', '#4F46E5']}
              style={modernStyles.itemIconContainer}>
              <MaterialCommunityIcons
                name="package-variant"
                size={22}
                color="white"
              />
            </LinearGradient>
            <View style={modernStyles.itemNameContainer}>
              <Sys.Text style={modernStyles.itemNameText}>
                {item.typeName}
              </Sys.Text>
              <View style={modernStyles.itemSubtextContainer}>
                <MaterialCommunityIcons
                  name="format-list-numbered"
                  size={14}
                  color={THEME.Color.gray}
                />
                <Sys.Text style={modernStyles.itemSubtext}>
                  {item.items.length} thiết bị
                </Sys.Text>
              </View>
            </View>
          </View>

          {/* Enhanced Quantity Badges */}
          <View style={modernStyles.quantityContainer}>
            <LinearGradient
              colors={['#EFF6FF', '#DBEAFE']}
              style={modernStyles.quantityBadge}>
              <MaterialCommunityIcons name="book-open" size={14} color="#3B82F6" />
              <Sys.Text style={modernStyles.quantityLabel}>Sổ sách</Sys.Text>
              <Sys.Text style={modernStyles.quantityValue}>
                {item.quantity}
              </Sys.Text>
            </LinearGradient>
            <LinearGradient
              colors={['#ECFDF5', '#D1FAE5']}
              style={[modernStyles.quantityBadge, modernStyles.realQuantityBadge]}>
              <MaterialCommunityIcons name="check-circle" size={14} color="#10B981" />
              <Sys.Text style={modernStyles.quantityLabel}>Thực tế</Sys.Text>
              <Sys.Text style={modernStyles.realQuantityValue}>
                {item.realQuantity || 0}
              </Sys.Text>
            </LinearGradient>
          </View>
        </LinearGradient>
      </LinearGradient>

      {/* Sub Items */}
      <View style={modernStyles.subItemsContainer}>
        {item.items.map((oneItem, index) => (
          <OneSubItem
            item={oneItem}
            key={`${index}-item-${item.typeName}`}
            handleCheckBoxPress={handleCheckBoxPress}
            index={index}
          />
        ))}
      </View>
    </Animated.View>
  );
};

const OneSubItem = ({item, handleCheckBoxPress, index}) => {
  const dispatch = useDispatch();
  const {equipmentStatus, mode} = useSelector(state => state.inventory);
  const [subItemScale] = useState(new Animated.Value(1));

  const handlePressIn = () => {
    Animated.spring(subItemScale, {
      toValue: 0.98,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(subItemScale, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const getStatusBadge = () => {
    const badges = [];

    if (item?.status && !item.isStatus && !item.isMissing) {
      badges.push({
        text: item?.status,
        color: item?.status_id === 1 ? '#10B981' : '#EF4444',
        icon: item?.status_id === 1 ? 'check-circle' : 'alert-circle',
      });
    }

    if (item.isNew) {
      badges.push({
        text: 'Mới',
        color: '#3B82F6',
        icon: 'new-box',
      });
    }

    if (item.isStatus) {
      const statusName = equipmentStatus[item.statusData.status_type]?.find(
        status => status.id === item.statusData.status_id,
      )?.name;
      badges.push({
        text:
          statusName?.length > 10
            ? `${statusName.slice(0, 10)}...`
            : statusName,
        color: '#F59E0B',
        icon: 'information',
      });
    }

    if (item.isMissing) {
      badges.push({
        text: 'Thiếu',
        color: '#EF4444',
        icon: 'alert-circle',
      });
    }

    if (item.tagChanged) {
      badges.push({
        text: item?.tagData?.oldTag ? 'Đổi tem' : 'Dán tem mới',
        color: '#8B5CF6',
        icon: 'tag',
      });
    }

    return badges;
  };

  return (
    <Animated.View
      style={[
        modernStyles.subItemContainer,
        {transform: [{scale: subItemScale}]},
      ]}>
      <LinearGradient
        colors={['#ffffff', '#fafafa']}
        style={modernStyles.subItemGradient}>

        {/* Main Content Row */}
        <View style={modernStyles.subItemMainContent}>
          {/* Photo */}
          {item.photo && (
            <View style={modernStyles.photoContainer}>
              <Image
                source={{
                  uri: mode === 1 || mode === 4 ? item.photo?.uri : item.photo,
                }}
                style={modernStyles.subItemImage}
                resizeMode="cover"
              />
            </View>
          )}

          {/* Details */}
          <View style={modernStyles.subItemDetails}>
            {item.tem_code && (
              <View style={modernStyles.detailRow}>
                <MaterialCommunityIcons
                  name="barcode"
                  size={14}
                  color={THEME.Color.gray}
                />
                <Sys.Text style={modernStyles.detailLabel}>Mã TTB:</Sys.Text>
                <Sys.Text style={modernStyles.detailValue}>
                  {item.tem_code}
                </Sys.Text>
              </View>
            )}

            <View style={modernStyles.detailRow}>
              <MaterialCommunityIcons
                name="calendar"
                size={14}
                color={THEME.Color.gray}
              />
              <Sys.Text style={modernStyles.detailLabel}>Ngày bàn giao:</Sys.Text>
              <Sys.Text style={modernStyles.detailValue}>
                {moment(item?.handover_date).format('DD/MM/YYYY')}
              </Sys.Text>
            </View>

            {/* Status Badges */}
            <View style={modernStyles.statusContainer}>
              {getStatusBadge().map((badge, badgeIndex) => (
                <View
                  key={badgeIndex}
                  style={[
                    modernStyles.statusBadge,
                    {backgroundColor: badge.color},
                  ]}>
                  <MaterialCommunityIcons
                    name={badge.icon}
                    size={12}
                    color="white"
                  />
                  <Sys.Text style={modernStyles.statusText}>
                    {badge.text}
                  </Sys.Text>
                </View>
              ))}
            </View>
          </View>

          {/* Actions */}
          <View style={modernStyles.actionsContainer}>
          {/* Checkbox for modes 1, 4, 3 */}
          {(mode === 1 || mode === 4 || mode === 3) && (
            <TouchableOpacity
              onPress={() =>
                mode === 1 || mode === 4 ? handleCheckBoxPress(item) : () => {}
              }
              style={modernStyles.checkboxContainer}
              activeOpacity={0.7}>
              <View
                style={[
                  modernStyles.checkbox,
                  item.checked && modernStyles.checkboxChecked,
                ]}>
                {item.checked && (
                  <MaterialCommunityIcons
                    name="check"
                    size={16}
                    color="white"
                  />
                )}
              </View>
            </TouchableOpacity>
          )}

          {/* Quantity for mode 2 */}
          {mode === 2 && (
            <View style={modernStyles.quantityDisplayContainer}>
              <Sys.Text style={modernStyles.quantityDisplayText}>
                {item?.latest_actual_qty}
              </Sys.Text>
            </View>
          )}

          {/* Enhanced Detail button */}
          <TouchableOpacity
            onPress={() => {
              dispatch(setCurrentItem(item));
              if (item?.isNew) {
                dispatch(
                  setAddNewItemModalVisible({
                    show: true,
                    isEdit: true,
                    typeId: item.supplier_type_id,
                  }),
                );
              } else {
                dispatch(setDetailedViewModalVisible({show: true}));
              }
            }}
            style={modernStyles.detailButton}
            activeOpacity={0.7}>
            <LinearGradient
              colors={['#EFF6FF', '#DBEAFE']}
              style={modernStyles.detailButtonGradient}>
              <MaterialCommunityIcons
                name="information-outline"
                size={16}
                color="#3B82F6"
              />
              <Sys.Text style={modernStyles.detailButtonText}>Chi tiết</Sys.Text>
              <MaterialCommunityIcons
                name="chevron-right"
                size={16}
                color="#3B82F6"
              />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  label: {},
  labelText: {
    fontSize: 12,
  },
  headerRight: {
    width: 35,
    height: 35,
    backgroundColor: '#0062FF',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    marginRight: 10,
    marginBottom: isAndroid ? -1 : 10,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    paddingHorizontal: 20,
  },
  sectionHeaderTextContainer: {
    flexBasis: 'auto',
    flexGrow: 1,
    flexShrink: 0,
  },
  sectionHeaderText: {
    flexBasis: 100,
    flexShrink: 1,
    flexGrow: 0,
    color: 'white',
    fontWeight: 'bold',
  },
  sectionHeaderTextQuantity: {
    flexBasis: 85,
    flexShrink: 1,
    flexGrow: 0,
    width: 100,
    alignItems: 'center',
    fontWeight: 'bold',
  },
  addButton: {
    padding: 10,
    backgroundColor: '#0062FF',
    borderBottomEndRadius: 10,
    borderBottomLeftRadius: 10,
    alignItems: 'center',
    marginBottom: 30,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  footer: {
    backgroundColor: 'white',
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextButton: {
    padding: 10,
    backgroundColor: '#0062FF',
    borderRadius: 50,
    paddingHorizontal: 40,
    flexDirection: 'row',
    alignItems: 'center',
  },
  nextButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  nextButtonIcon: {
    position: 'absolute',
    right: 15,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalText: {
    marginBottom: 10,
    textAlign: 'center',
    fontSize: 14,
  },
  itemContainer: {
    flexDirection: 'row',
    padding: 10,
    paddingHorizontal: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#D1D1D1',
    height: 55,
    alignItems: 'center',
  },
  itemName: {
    flex: 1,
  },
  itemNameText: {
    fontWeight: '700',
  },
  itemQuantity: {
    width: 100,
  },
  itemQuantityText: {
    textAlign: 'center',
    color: '#90BB3B',
    fontSize: 16,
    fontWeight: 'bold',
  },
  itemRealQuantityText: {
    textAlign: 'center',
    color: '#FF0000',
    fontSize: 16,
    fontWeight: 'bold',
  },
  subItemContainer: {
    flexDirection: 'row',
    padding: 10,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#D1D1D1',
    alignItems: 'center',
  },
  subItemDetails: {
    flex: 1,
  },
  subItemData: {
    width: 100,
    marginRight: 10,
  },
  subItemDataText: {
    textAlign: 'right',
    color: '#848484',
  },
  subItemStatus: {
    color: '#90BB3B',
    textAlign: 'right',
  },
  iconDetail: {
    paddingVertical: 20,
    paddingLeft: 20,
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
  checkBoxContainer: {
    marginHorizontal: 30,
    marginRight: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptySection: {
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptySectionText: {
    paddingVertical: 50,
    color: '#848484',
  },
  subItemImage: {
    width: 50, // Set your desired width
    height: 50, // Set your desired height
    marginRight: 10,
    borderRadius: 5, // Optional: if you want rounded corners
  },
});

// Modern Styles
const modernStyles = StyleSheet.create({
  contentContainer: {
    flex: 1,
  },
  headerRight: {
    marginRight: 8,
  },
  headerRightGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },

  // Section Header Styles
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeaderLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  sectionTitleContainer: {
    flex: 1,
  },
  sectionHeaderTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  sectionHeaderSubtitle: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginTop: 2,
  },
  sectionHeaderRight: {
    flexDirection: 'row',
  },
  quantityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 80,
    justifyContent: 'center',
  },
  quantityHeaderText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginLeft: 4,
  },

  // Empty Section Styles
  emptySection: {
    alignItems: 'center',
    paddingVertical: 40,
    backgroundColor: 'white',
  },
  emptySectionText: {
    fontSize: 16,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginTop: 12,
  },
  emptySectionSubtext: {
    fontSize: 14,
    color: THEME.Color.grayLight,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginTop: 4,
    textAlign: 'center',
  },

  // Add Button Styles
  addButton: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    overflow: 'hidden',
  },
  addButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginLeft: 8,
  },

  // Footer Styles
  footer: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  nextButtonContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  nextButtonIcon: {
    marginRight: 8,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    flex: 1,
    textAlign: 'center',
  },

  // Item Styles
  itemWrapper: {
    marginHorizontal: 16,
    marginVertical: 4,
  },
  itemContainer: {
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
    overflow: 'hidden',
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  itemHeaderLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  itemNameContainer: {
    flex: 1,
  },
  itemNameText: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    letterSpacing: 0.3,
  },
  itemSubtextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  itemSubtext: {
    fontSize: 13,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginLeft: 6,
  },
  quantityContainer: {
    flexDirection: 'row',
  },
  quantityBadge: {
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    marginLeft: 8,
    minWidth: 70,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  realQuantityBadge: {
    // Gradient colors handled in component
  },
  quantityLabel: {
    fontSize: 10,
    color: '#64748B',
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginTop: 2,
  },
  quantityValue: {
    fontSize: 16,
    fontWeight: '800',
    color: '#1E293B',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginTop: 4,
  },
  realQuantityValue: {
    fontSize: 16,
    fontWeight: '800',
    color: '#059669',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginTop: 4,
  },

  // Sub Item Styles
  subItemsContainer: {
    paddingLeft: 16,
  },
  subItemContainer: {
    marginBottom: 8,
    marginRight: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  subItemGradient: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderLeftWidth: 3,
    borderLeftColor: THEME.Color.primary,
  },
  photoContainer: {
    marginRight: 12,
  },
  subItemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  subItemDetails: {
    flex: 1,
    marginRight: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  detailLabel: {
    fontSize: 12,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginLeft: 6,
    minWidth: 80,
  },
  detailValue: {
    fontSize: 12,
    fontWeight: '600',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    flex: 1,
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 6,
    marginRight: 6,
    marginBottom: 4,
  },
  statusText: {
    fontSize: 10,
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginLeft: 3,
  },
  actionsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxContainer: {
    marginBottom: 8,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: THEME.Color.gray,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  checkboxChecked: {
    backgroundColor: THEME.Color.primary,
    borderColor: THEME.Color.primary,
  },
  quantityDisplayContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    borderRadius: 6,
    marginBottom: 8,
  },
  quantityDisplayText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#10B981',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  detailButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  detailButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    minWidth: 80,
  },
  detailButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#3B82F6',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginHorizontal: 6,
  },
});
