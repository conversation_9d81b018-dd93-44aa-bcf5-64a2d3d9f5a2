import React, {useState} from 'react';
import {
  Animated,
  Dimensions,
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Modal from 'react-native-modal';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Sys from '../../../components/Sys';
import THEME from '../../../components/theme/theme';
import CloseIcon from '../../report/icons/CloseIcon';
import StatusIcon from '../icons/StatusIcon';
import MissingIcon from '../icons/MissingIcon';
import ChangeIcon from '../icons/ChangeIcon';
import moment from 'moment/moment';
import Divider from './Divider';
import StatusUpdateModal from './StatusUpdateModal';
import ReportMissingModal from './ReportMissingModal';
import TagChangeModal from './TagChangeModal';
import {useDispatch, useSelector} from 'react-redux';
import {
  handleModalHide,
  setDetailedViewModalVisible,
} from '../services/inventory.slice';
import TemInfoModal from './TemInfoModal';
import BlinkingBadge from './BlinkingBadge';
import {AccountSelectors} from '../../account/services/account.slice';
import {SUPPLIER_TYPE_VIETLOTT_ID} from '../services/const';
import {useNavigation} from '@react-navigation/native';
import Color from '../../../components/theme/Color';

const {width, height} = Dimensions.get('window');

const DetailedViewModal = React.memo(({onAction, item, page = undefined}) => {
  const dispatch = useDispatch();
  const {isDetailedViewModalVisible, mode} = useSelector(
    state => state.inventory,
  );
  const isPos = useSelector(AccountSelectors.isPos);

  // Animation states
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  const [activeTab, setActiveTab] = useState('details'); // 'details', 'history', 'tags'

  // Initialize animations when modal opens
  React.useEffect(() => {
    if (isDetailedViewModalVisible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(0);
      slideAnim.setValue(50);
    }
  }, [isDetailedViewModalVisible]);

  const canChangeTag =
    mode !== 3
      ? (item?.product?.hasTem || item?.tem_code) && !item?.isMissing
      : item?.tagChanged;

  const canReportMissing = mode !== 3 ? !item?.tagData : item?.isMissing;

  const canStatus = mode !== 3 || item?.isStatus;

  const canSeeChangeTag =
    !isPos &&
    item?.supplier_type_id === SUPPLIER_TYPE_VIETLOTT_ID &&
    (item?.product?.hasTem || item?.tem_code);

  const navigation = useNavigation();

  const isPosWarehouse = item?.warehouse?.type === 'App\\Pos';
  const handleNavigation = () => {
    if (isPosWarehouse && page) {
      dispatch(setDetailedViewModalVisible({show: false}));
      navigation.navigate(page, {
        posCode: item?.pos_id,
      });
    }
  };
  return (
    <>
      {item && (
        <View>
          <Modal
            animationIn={'fadeInUp'}
            animationOut={'fadeOutDown'}
            style={styles.modalStyle}
            isVisible={isDetailedViewModalVisible}
            // onSwipeComplete={() =>
            //   dispatch(setDetailedViewModalVisible({show: false}))
            // }
            propagateSwipe={true}
            // swipeDirection="down"
            onModalHide={() => {
              dispatch(handleModalHide());
            }}>
            <View style={modernStyles.modalContent}>
              {/* Modern Header */}
              <LinearGradient
                colors={['#6366F1', '#4F46E5']}
                style={modernStyles.header}
                start={{x: 0, y: 0}}
                end={{x: 1, y: 0}}>
                <View style={modernStyles.headerLeft}>
                  <MaterialCommunityIcons
                    name="information"
                    size={24}
                    color="white"
                  />
                  <View style={modernStyles.headerTextContainer}>
                    <Sys.Text style={modernStyles.headerTitle}>
                      Chi tiết trang thiết bị
                    </Sys.Text>
                    <Sys.Text style={modernStyles.headerSubtitle}>
                      {item?.name || 'Thông tin thiết bị'}
                    </Sys.Text>
                  </View>
                </View>
                <TouchableOpacity
                  onPress={() =>
                    dispatch(setDetailedViewModalVisible({show: false}))
                  }
                  style={modernStyles.closeButton}>
                  <MaterialCommunityIcons
                    name="close"
                    size={24}
                    color="white"
                  />
                </TouchableOpacity>
              </LinearGradient>

              {/* Main Info Card */}
              <Animated.View
                style={[
                  modernStyles.mainCard,
                  {
                    opacity: fadeAnim,
                    transform: [{translateY: slideAnim}],
                  },
                ]}>
                <LinearGradient
                  colors={['#ffffff', '#f8f9fa']}
                  style={modernStyles.mainCardGradient}>
                  <View style={modernStyles.imageContainer}>
                    <Image
                      style={modernStyles.deviceImage}
                      source={{uri: item.image}}
                    />
                    <View style={modernStyles.statusBadgeContainer}>
                      <View
                        style={[
                          modernStyles.statusBadge,
                          {
                            backgroundColor: item?.status_id === 1 ? '#10B981' : '#EF4444',
                          },
                        ]}>
                        <MaterialCommunityIcons
                          name={item?.status_id === 1 ? 'check-circle' : 'alert-circle'}
                          size={12}
                          color="white"
                        />
                        <Sys.Text style={modernStyles.statusBadgeText}>
                          {item.status}
                        </Sys.Text>
                      </View>
                    </View>
                  </View>

                  <View style={modernStyles.infoContainer}>
                    <View style={modernStyles.infoRow}>
                      <MaterialCommunityIcons name="tag" size={16} color={THEME.Color.gray} />
                      <Sys.Text style={modernStyles.infoLabel}>Tên thiết bị</Sys.Text>
                      <Sys.Text style={modernStyles.infoValue}>{item.name}</Sys.Text>
                    </View>

                    <View style={modernStyles.infoRow}>
                      <MaterialCommunityIcons name="barcode" size={16} color={THEME.Color.gray} />
                      <Sys.Text style={modernStyles.infoLabel}>Mã TTB</Sys.Text>
                      <Sys.Text style={modernStyles.infoValue}>{item.tem_code}</Sys.Text>
                    </View>

                    <View style={modernStyles.infoRow}>
                      <MaterialCommunityIcons name="code-tags" size={16} color={THEME.Color.gray} />
                      <Sys.Text style={modernStyles.infoLabel}>Mã loại TTB</Sys.Text>
                      <Sys.Text style={modernStyles.infoValue}>{item.product.code}</Sys.Text>
                    </View>
                  </View>
                </LinearGradient>
              </Animated.View>
              {/* Warning Card */}
              {item.notExit && (
                <Animated.View
                  style={[
                    modernStyles.warningCard,
                    {
                      opacity: fadeAnim,
                      transform: [{translateY: slideAnim}],
                    },
                  ]}>
                  <LinearGradient
                    colors={['#F59E0B', '#D97706']}
                    style={modernStyles.warningGradient}>
                    <MaterialCommunityIcons
                      name="alert-circle"
                      size={24}
                      color="white"
                    />
                    <View style={modernStyles.warningTextContainer}>
                      <Sys.Text style={modernStyles.warningTitle}>
                        Thiết bị thuộc ĐBH khác
                      </Sys.Text>
                      <Sys.Text style={modernStyles.warningText}>
                        Theo sổ sách sản phẩm hiện đang thuộc ĐBH khác có mã là: {item?.pos_id}
                      </Sys.Text>
                    </View>
                  </LinearGradient>
                </Animated.View>
              )}
              {/* Action Buttons */}
              {!item.notExit && (
                <Animated.View
                  style={[
                    modernStyles.actionsCard,
                    {
                      opacity: fadeAnim,
                      transform: [{translateY: slideAnim}],
                    },
                  ]}>
                  <LinearGradient
                    colors={['#ffffff', '#f8f9fa']}
                    style={modernStyles.actionsGradient}>

                    {/* Status Button */}
                    <TouchableOpacity
                      onPress={() =>
                        dispatch(
                          setDetailedViewModalVisible({
                            show: false,
                            nextModal: 'isStatusUpdateModalVisible',
                          }),
                        )
                      }
                      disabled={!canStatus}
                      style={[
                        modernStyles.actionButton,
                        !canStatus && modernStyles.actionButtonDisabled,
                      ]}>
                      <LinearGradient
                        colors={canStatus ? ['#3B82F6', '#1D4ED8'] : ['#9CA3AF', '#6B7280']}
                        style={modernStyles.actionButtonGradient}>
                        <MaterialCommunityIcons
                          name="clipboard-check"
                          size={20}
                          color="white"
                        />
                        {item.isStatus && (
                          <View style={modernStyles.badgeIndicator}>
                            <BlinkingBadge />
                          </View>
                        )}
                      </LinearGradient>
                      <Sys.Text style={modernStyles.actionButtonText}>
                        Tình trạng TTB
                      </Sys.Text>
                    </TouchableOpacity>

                    {/* Missing Button */}
                    <TouchableOpacity
                      onPress={() =>
                        dispatch(
                          setDetailedViewModalVisible({
                            show: false,
                            nextModal: 'isReportMissingModalVisible',
                          }),
                        )
                      }
                      disabled={!canReportMissing}
                      style={[
                        modernStyles.actionButton,
                        !canReportMissing && modernStyles.actionButtonDisabled,
                      ]}>
                      <LinearGradient
                        colors={canReportMissing ? ['#EF4444', '#DC2626'] : ['#9CA3AF', '#6B7280']}
                        style={modernStyles.actionButtonGradient}>
                        <MaterialCommunityIcons
                          name="alert-circle"
                          size={20}
                          color="white"
                        />
                        {item.isMissing && (
                          <View style={modernStyles.badgeIndicator}>
                            <BlinkingBadge />
                          </View>
                        )}
                      </LinearGradient>
                      <Sys.Text style={modernStyles.actionButtonText}>
                        Báo thiếu
                      </Sys.Text>
                    </TouchableOpacity>

                    {/* Change Tag Button */}
                    {canSeeChangeTag && (
                      <TouchableOpacity
                        onPress={() =>
                          dispatch(
                            setDetailedViewModalVisible({
                              show: false,
                              nextModal: 'isTagChangeModalVisible',
                            }),
                          )
                        }
                        disabled={!canChangeTag}
                        style={[
                          modernStyles.actionButton,
                          !canChangeTag && modernStyles.actionButtonDisabled,
                        ]}>
                        <LinearGradient
                          colors={canChangeTag ? ['#10B981', '#059669'] : ['#9CA3AF', '#6B7280']}
                          style={modernStyles.actionButtonGradient}>
                          <MaterialCommunityIcons
                            name="tag-multiple"
                            size={20}
                            color="white"
                          />
                          {item.tagChanged && (
                            <View style={modernStyles.badgeIndicator}>
                              <BlinkingBadge />
                            </View>
                          )}
                        </LinearGradient>
                        <Sys.Text style={modernStyles.actionButtonText}>
                          {item?.tem_code ? 'Đổi tem' : 'Dán tem mới'}
                        </Sys.Text>
                      </TouchableOpacity>
                    )}
                  </LinearGradient>
                </Animated.View>
              )}
              {/* Tab Navigation */}
              <View style={modernStyles.tabContainer}>
                <TouchableOpacity
                  onPress={() => setActiveTab('details')}
                  style={[
                    modernStyles.tab,
                    activeTab === 'details' && modernStyles.activeTab,
                  ]}>
                  <MaterialCommunityIcons
                    name="information"
                    size={16}
                    color={activeTab === 'details' ? '#6366F1' : THEME.Color.gray}
                  />
                  <Sys.Text
                    style={[
                      modernStyles.tabText,
                      activeTab === 'details' && modernStyles.activeTabText,
                    ]}>
                    Chi tiết
                  </Sys.Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => setActiveTab('history')}
                  style={[
                    modernStyles.tab,
                    activeTab === 'history' && modernStyles.activeTab,
                  ]}>
                  <MaterialCommunityIcons
                    name="history"
                    size={16}
                    color={activeTab === 'history' ? '#6366F1' : THEME.Color.gray}
                  />
                  <Sys.Text
                    style={[
                      modernStyles.tabText,
                      activeTab === 'history' && modernStyles.activeTabText,
                    ]}>
                    Lịch sử
                  </Sys.Text>
                </TouchableOpacity>

                {item?.tem_code && (
                  <TouchableOpacity
                    onPress={() => setActiveTab('tags')}
                    style={[
                      modernStyles.tab,
                      activeTab === 'tags' && modernStyles.activeTab,
                    ]}>
                    <MaterialCommunityIcons
                      name="tag"
                      size={16}
                      color={activeTab === 'tags' ? '#6366F1' : THEME.Color.gray}
                    />
                    <Sys.Text
                      style={[
                        modernStyles.tabText,
                        activeTab === 'tags' && modernStyles.activeTabText,
                      ]}>
                      Tem
                    </Sys.Text>
                  </TouchableOpacity>
                )}
              </View>

              {/* Tab Content */}
              <ScrollView
                style={modernStyles.scrollContainer}
                showsVerticalScrollIndicator={false}
                nestedScrollEnabled={true}>
                {/* Details Tab */}
                {activeTab === 'details' && (
                  <View style={modernStyles.tabContent}>
                    <LinearGradient
                      colors={['#ffffff', '#f8f9fa']}
                      style={modernStyles.detailsCard}>

                      {/* Warehouse Info */}
                      <View style={modernStyles.sectionHeader}>
                        <MaterialCommunityIcons
                          name="warehouse"
                          size={20}
                          color={THEME.Color.primary}
                        />
                        <Sys.Text style={modernStyles.sectionTitle}>
                          Thông tin kho
                        </Sys.Text>
                      </View>

                      <View style={modernStyles.detailRow}>
                        <MaterialCommunityIcons name="home" size={16} color={THEME.Color.gray} />
                        <Sys.Text style={modernStyles.detailLabel}>Tên kho</Sys.Text>
                        <TouchableOpacity onPress={handleNavigation}>
                          <Sys.Text
                            style={[
                              modernStyles.detailValue,
                              isPosWarehouse && modernStyles.clickableText,
                            ]}>
                            {item?.warehouse?.name}
                          </Sys.Text>
                        </TouchableOpacity>
                      </View>

                      <View style={modernStyles.detailRow}>
                        <MaterialCommunityIcons name="tag" size={16} color={THEME.Color.gray} />
                        <Sys.Text style={modernStyles.detailLabel}>Loại kho</Sys.Text>
                        <Sys.Text style={modernStyles.detailValue}>
                          {item?.warehouse?.type_label}
                        </Sys.Text>
                      </View>

                      {/* Dates Info */}
                      <View style={modernStyles.sectionHeader}>
                        <MaterialCommunityIcons
                          name="calendar"
                          size={20}
                          color={THEME.Color.primary}
                        />
                        <Sys.Text style={modernStyles.sectionTitle}>
                          Thông tin thời gian
                        </Sys.Text>
                      </View>

                      {isPosWarehouse && (
                        <View style={modernStyles.detailRow}>
                          <MaterialCommunityIcons name="handshake" size={16} color={THEME.Color.gray} />
                          <Sys.Text style={modernStyles.detailLabel}>Ngày bàn giao</Sys.Text>
                          <Sys.Text style={modernStyles.detailValue}>
                            {moment(item?.handover_date).format('DD/MM/YYYY')}
                          </Sys.Text>
                        </View>
                      )}

                      <View style={modernStyles.detailRow}>
                        <MaterialCommunityIcons name="play-circle" size={16} color={THEME.Color.gray} />
                        <Sys.Text style={modernStyles.detailLabel}>Ngày sử dụng</Sys.Text>
                        <Sys.Text style={modernStyles.detailValue}>
                          {moment(item?.start_use_date).format('DD/MM/YYYY')}
                        </Sys.Text>
                      </View>

                      {item?.warranty_end_date && (
                        <View style={modernStyles.detailRow}>
                          <MaterialCommunityIcons name="shield-check" size={16} color={THEME.Color.gray} />
                          <Sys.Text style={modernStyles.detailLabel}>Hết bảo hành</Sys.Text>
                          <Sys.Text style={modernStyles.detailValue}>
                            {moment(item?.warranty_end_date).format('DD/MM/YYYY')}
                          </Sys.Text>
                        </View>
                      )}
                    </LinearGradient>
                  </View>
                )}
                {/* History Tab */}
                {activeTab === 'history' && (
                  <View style={modernStyles.tabContent}>
                    <LinearGradient
                      colors={['#ffffff', '#f8f9fa']}
                      style={modernStyles.historyCard}>

                      <View style={modernStyles.sectionHeader}>
                        <MaterialCommunityIcons
                          name="history"
                          size={20}
                          color={THEME.Color.primary}
                        />
                        <Sys.Text style={modernStyles.sectionTitle}>
                          Lịch sử giao dịch
                        </Sys.Text>
                      </View>

                      {item.itemHistory && item.itemHistory.length > 0 ? (
                        item.itemHistory.map((history, index) => (
                          <View key={index} style={modernStyles.historyItem}>
                            <View style={modernStyles.historyItemHeader}>
                              <View style={modernStyles.historyIndex}>
                                <Sys.Text style={modernStyles.historyIndexText}>
                                  {index + 1}
                                </Sys.Text>
                              </View>
                              <View style={modernStyles.historyMainInfo}>
                                <Sys.Text style={modernStyles.historyTitle}>
                                  {history.name}
                                </Sys.Text>
                                <Sys.Text style={modernStyles.historySubtitle}>
                                  {history.action_label}
                                </Sys.Text>
                              </View>
                              <View style={modernStyles.historyDate}>
                                <Sys.Text style={modernStyles.historyDateText}>
                                  {moment(history.actual_date).format('DD/MM/YYYY')}
                                </Sys.Text>
                              </View>
                            </View>

                            <View style={modernStyles.historyDetails}>
                              <View style={modernStyles.historyDetailRow}>
                                <MaterialCommunityIcons name="barcode" size={14} color={THEME.Color.gray} />
                                <Sys.Text style={modernStyles.historyDetailLabel}>Mã tem:</Sys.Text>
                                <Sys.Text style={modernStyles.historyDetailValue}>
                                  {history.tem_code}
                                </Sys.Text>
                              </View>
                            </View>
                          </View>
                        ))
                      ) : (
                        <View style={modernStyles.emptyState}>
                          <MaterialCommunityIcons
                            name="history"
                            size={48}
                            color={THEME.Color.grayLight}
                          />
                          <Sys.Text style={modernStyles.emptyStateText}>
                            Chưa có lịch sử giao dịch
                          </Sys.Text>
                        </View>
                      )}
                    </LinearGradient>
                  </View>
                )}

                {/* Tags Tab */}
                {activeTab === 'tags' && item?.tem_code && (
                  <View style={modernStyles.tabContent}>
                    <LinearGradient
                      colors={['#ffffff', '#f8f9fa']}
                      style={modernStyles.tagsCard}>

                      <View style={modernStyles.sectionHeader}>
                        <MaterialCommunityIcons
                          name="tag-multiple"
                          size={20}
                          color={THEME.Color.primary}
                        />
                        <Sys.Text style={modernStyles.sectionTitle}>
                          Lịch sử thay tem
                        </Sys.Text>
                      </View>

                      {item.temHistory && item.temHistory.length > 0 ? (
                        item.temHistory.map((history, index) => (
                          <View key={index} style={modernStyles.tagItem}>
                            <View style={modernStyles.tagItemHeader}>
                              <View style={modernStyles.tagIndex}>
                                <Sys.Text style={modernStyles.tagIndexText}>
                                  {index + 1}
                                </Sys.Text>
                              </View>
                              <View style={modernStyles.tagMainInfo}>
                                <View style={modernStyles.tagCodeContainer}>
                                  <View style={modernStyles.tagCodeBox}>
                                    <MaterialCommunityIcons name="tag-remove" size={14} color="#EF4444" />
                                    <Sys.Text style={modernStyles.oldTagText}>
                                      {history?.previous_tem_code || 'N/A'}
                                    </Sys.Text>
                                  </View>
                                  <MaterialCommunityIcons name="arrow-right" size={16} color={THEME.Color.gray} />
                                  <View style={modernStyles.tagCodeBox}>
                                    <MaterialCommunityIcons name="tag" size={14} color="#10B981" />
                                    <Sys.Text style={modernStyles.newTagText}>
                                      {history.tem_code}
                                    </Sys.Text>
                                  </View>
                                </View>
                                <View style={modernStyles.tagStatus}>
                                  <View
                                    style={[
                                      modernStyles.statusIndicator,
                                      {backgroundColor: history.tem_status_label === 'Hoạt động' ? '#10B981' : '#EF4444'},
                                    ]}>
                                    <Sys.Text style={modernStyles.statusText}>
                                      {history.tem_status_label}
                                    </Sys.Text>
                                  </View>
                                </View>
                              </View>
                            </View>

                            <View style={modernStyles.tagDetails}>
                              <View style={modernStyles.tagDetailRow}>
                                <MaterialCommunityIcons name="calendar-clock" size={14} color={THEME.Color.gray} />
                                <Sys.Text style={modernStyles.tagDetailLabel}>Thời gian:</Sys.Text>
                                <Sys.Text style={modernStyles.tagDetailValue}>
                                  {moment(history.actual_date).format('DD/MM/YYYY HH:mm:ss')}
                                </Sys.Text>
                              </View>
                            </View>
                          </View>
                        ))
                      ) : (
                        <View style={modernStyles.emptyState}>
                          <MaterialCommunityIcons
                            name="tag-off"
                            size={48}
                            color={THEME.Color.grayLight}
                          />
                          <Sys.Text style={modernStyles.emptyStateText}>
                            Chưa có lịch sử thay tem
                          </Sys.Text>
                        </View>
                      )}
                    </LinearGradient>
                  </View>
                )}
              </ScrollView>
            </View>
          </Modal>
          <StatusUpdateModal item={item} onAction={onAction} />
          <ReportMissingModal item={item} onAction={onAction} />
          <TagChangeModal item={item} onAction={onAction} />
        </View>
      )}
      <TemInfoModal />
    </>
  );
});

// Modern Styles
const modernStyles = StyleSheet.create({
  modalContent: {
    backgroundColor: '#f8f9fa',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingBottom: 20,
    maxHeight: height * 0.9,
  },

  // Header Styles
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginTop: 2,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Main Card Styles
  mainCard: {
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  mainCardGradient: {
    flexDirection: 'row',
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  imageContainer: {
    marginRight: 16,
    position: 'relative',
  },
  deviceImage: {
    width: 80,
    height: 80,
    borderRadius: 12,
    backgroundColor: '#f0f0f0',
  },
  statusBadgeContainer: {
    position: 'absolute',
    bottom: -8,
    left: -8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  statusBadgeText: {
    fontSize: 10,
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginLeft: 4,
  },
  infoContainer: {
    flex: 1,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 12,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginLeft: 8,
    minWidth: 80,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    flex: 1,
    textAlign: 'right',
  },

  // Warning Card Styles
  warningCard: {
    marginHorizontal: 16,
    marginTop: 12,
    borderRadius: 16,
    overflow: 'hidden',
  },
  warningGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  warningTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  warningTitle: {
    fontSize: 14,
    fontWeight: '700',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  warningText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginTop: 4,
    lineHeight: 18,
  },

  // Actions Card Styles
  actionsCard: {
    marginHorizontal: 16,
    marginTop: 12,
    borderRadius: 16,
    overflow: 'hidden',
  },
  actionsGradient: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  actionButton: {
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 8,
  },
  actionButtonDisabled: {
    opacity: 0.5,
  },
  actionButtonGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
    position: 'relative',
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 16,
  },
  badgeIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
  },

  // Tab Styles
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginTop: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: '#6366F1',
  },
  tabText: {
    fontSize: 12,
    fontWeight: '600',
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Medium'],
    marginLeft: 6,
  },
  activeTabText: {
    color: 'white',
  },

  // Content Styles
  scrollContainer: {
    flex: 1,
    marginTop: 12,
  },
  tabContent: {
    paddingHorizontal: 16,
  },

  // Details Card Styles
  detailsCard: {
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginLeft: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 8,
  },
  detailLabel: {
    fontSize: 13,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginLeft: 8,
    minWidth: 100,
  },
  detailValue: {
    fontSize: 13,
    fontWeight: '600',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    flex: 1,
    textAlign: 'right',
  },
  clickableText: {
    color: '#EF4444',
    textDecorationLine: 'underline',
  },

  // History Card Styles
  historyCard: {
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  historyItem: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#6366F1',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  historyItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  historyIndex: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#6366F1',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  historyIndexText: {
    fontSize: 12,
    fontWeight: '700',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  historyMainInfo: {
    flex: 1,
  },
  historyTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  historySubtitle: {
    fontSize: 12,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginTop: 2,
  },
  historyDate: {
    alignItems: 'flex-end',
  },
  historyDateText: {
    fontSize: 12,
    fontWeight: '600',
    color: THEME.Color.primary,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  historyDetails: {
    paddingLeft: 44,
  },
  historyDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  historyDetailLabel: {
    fontSize: 12,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginLeft: 6,
  },
  historyDetailValue: {
    fontSize: 12,
    fontWeight: '600',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginLeft: 8,
  },

  // Tags Card Styles
  tagsCard: {
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  tagItem: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#10B981',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  tagItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tagIndex: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#10B981',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  tagIndexText: {
    fontSize: 12,
    fontWeight: '700',
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  tagMainInfo: {
    flex: 1,
  },
  tagCodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tagCodeBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginHorizontal: 4,
  },
  oldTagText: {
    fontSize: 11,
    color: '#EF4444',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginLeft: 4,
  },
  newTagText: {
    fontSize: 11,
    color: '#10B981',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginLeft: 4,
  },
  tagStatus: {
    alignItems: 'flex-start',
  },
  statusIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    color: 'white',
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
  },
  tagDetails: {
    paddingLeft: 44,
  },
  tagDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagDetailLabel: {
    fontSize: 12,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginLeft: 6,
  },
  tagDetailValue: {
    fontSize: 12,
    fontWeight: '600',
    color: THEME.Color.text,
    fontFamily: THEME.FrontFamily['Roboto-Bold'],
    marginLeft: 8,
  },

  // Empty State Styles
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 14,
    color: THEME.Color.gray,
    fontFamily: THEME.FrontFamily['Roboto-Regular'],
    marginTop: 12,
    textAlign: 'center',
  },
});

export default DetailedViewModal;

const styles = StyleSheet.create({
  modalStyle: {
    padding: 0,
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#E7E7E7',
    padding: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
  },
  card: {
    marginTop: 10,
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 10,
  },
  col: {
    flexGrow: 1,
    flexBasis: 0,
  },
  col2p3: {
    flexGrow: 2,
    flexBasis: 0,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalText: {
    marginBottom: 10,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
  },
  titleText: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  titleTextDate: {
    marginBottom: 5,
  },
  detailText: {
    marginBottom: 5,
    fontSize: 14,
    fontWeight: 600,
  },
  titleHeadText: {
    color: '#001451',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },

  historyHeadText: {
    color: '#FFFFFF',
    fontSize: 12,
    flex: 1,
    textAlign: 'left',
    minWidth: 50,
  },
  historyText: {
    fontSize: 10,
    flex: 1,
    textAlign: 'left',
    minWidth: 50,
  },

  historyRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 5,
  },
  history: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    backgroundColor: '#0062FF',
    padding: 5,
  },
});
