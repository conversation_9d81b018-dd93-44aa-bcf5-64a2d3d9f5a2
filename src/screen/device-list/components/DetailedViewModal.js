import React from 'react';
import {
  Dimensions,
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Modal from 'react-native-modal';
import Sys from '../../../components/Sys';
import CloseIcon from '../../report/icons/CloseIcon';
import StatusIcon from '../icons/StatusIcon';
import MissingIcon from '../icons/MissingIcon';
import ChangeIcon from '../icons/ChangeIcon';
import moment from 'moment/moment';
import Divider from './Divider';
import StatusUpdateModal from './StatusUpdateModal';
import ReportMissingModal from './ReportMissingModal';
import TagChangeModal from './TagChangeModal';
import {useDispatch, useSelector} from 'react-redux';
import {
  handleModalHide,
  setDetailedViewModalVisible,
} from '../services/inventory.slice';
import TemInfoModal from './TemInfoModal';
import BlinkingBadge from './BlinkingBadge';
import {AccountSelectors} from '../../account/services/account.slice';
import {SUPPLIER_TYPE_VIETLOTT_ID} from '../services/const';
import {useNavigation} from '@react-navigation/native';
import Color from '../../../components/theme/Color';

const {width, height} = Dimensions.get('window');

const DetailedViewModal = React.memo(({onAction, item, page = undefined}) => {
  const dispatch = useDispatch();
  const {isDetailedViewModalVisible, mode} = useSelector(
    state => state.inventory,
  );
  const isPos = useSelector(AccountSelectors.isPos);

  const canChangeTag =
    mode !== 3
      ? (item?.product?.hasTem || item?.tem_code) && !item?.isMissing
      : item?.tagChanged;

  const canReportMissing = mode !== 3 ? !item?.tagData : item?.isMissing;

  const canStatus = mode !== 3 || item?.isStatus;

  const canSeeChangeTag =
    !isPos &&
    item?.supplier_type_id === SUPPLIER_TYPE_VIETLOTT_ID &&
    (item?.product?.hasTem || item?.tem_code);

  const navigation = useNavigation();

  const isPosWarehouse = item?.warehouse?.type === 'App\\Pos';
  const handleNavigation = () => {
    if (isPosWarehouse && page) {
      dispatch(setDetailedViewModalVisible({show: false}));
      navigation.navigate(page, {
        posCode: item?.pos_id,
      });
    }
  };
  return (
    <>
      {item && (
        <View>
          <Modal
            animationIn={'fadeInUp'}
            animationOut={'fadeOutDown'}
            style={styles.modalStyle}
            isVisible={isDetailedViewModalVisible}
            // onSwipeComplete={() =>
            //   dispatch(setDetailedViewModalVisible({show: false}))
            // }
            propagateSwipe={true}
            // swipeDirection="down"
            onModalHide={() => {
              dispatch(handleModalHide());
            }}>
            <View style={styles.modalContent}>
              <View style={styles.header}>
                <Sys.Text style={styles.modalText}>
                  Thông tin chi tiết trang thiết bị
                </Sys.Text>
                <TouchableOpacity
                  onPress={() =>
                    dispatch(setDetailedViewModalVisible({show: false}))
                  }>
                  <CloseIcon />
                </TouchableOpacity>
              </View>

              <View style={[styles.card, styles.row]}>
                <Image
                  style={[
                    styles.col,
                    {
                      height: 100,
                      borderRadius: 5,
                      marginRight: 20,
                    },
                  ]}
                  source={{uri: item.image}}
                />
                <View style={styles.col2p3}>
                  <View style={styles.titleText}>
                    <Sys.Text style={styles.detailText}>Tên:</Sys.Text>
                    <Sys.Text style={styles.detailText}>{item.name}</Sys.Text>
                  </View>
                  <View style={styles.titleText}>
                    <Sys.Text style={styles.detailText}>Mã TTB:</Sys.Text>
                    <Sys.Text style={styles.detailText}>
                      {item.tem_code}
                    </Sys.Text>
                  </View>
                  <View style={styles.titleText}>
                    <Sys.Text style={styles.detailText}>Mã loại TTB:</Sys.Text>
                    <Sys.Text style={styles.detailText}>
                      {item.product.code}
                    </Sys.Text>
                  </View>
                  <View style={styles.titleText}>
                    <Sys.Text style={styles.detailText}>Tình trạng:</Sys.Text>
                    <Sys.Text style={styles.detailText}>{item.status}</Sys.Text>
                  </View>
                </View>
              </View>
              {item.notExit && (
                <View
                  style={[
                    styles.card,
                    {
                      justifyContent: 'space-around',
                      backgroundColor: '#EF7721',
                      width: '100%',
                    },
                  ]}>
                  <View>
                    <Sys.Text style={{color: 'white', lineHeight: 18}}>
                      Theo sổ sách sản phẩm hiện đang thuộc ĐBH khác có mã là :{' '}
                      {item?.pos_id}
                    </Sys.Text>
                  </View>
                </View>
              )}
              {!item.notExit && (
                <View
                  style={[
                    styles.card,
                    styles.row,
                    {
                      justifyContent: 'space-around',
                    },
                  ]}>
                  <View style={[styles.col]}>
                    <TouchableOpacity
                      onPress={() =>
                        dispatch(
                          setDetailedViewModalVisible({
                            show: false,
                            nextModal: 'isStatusUpdateModalVisible',
                          }),
                        )
                      }
                      disabled={!canStatus}
                      style={{
                        alignItems: 'center',
                        gap: 5,
                        opacity: canStatus ? 1 : 0.4,
                      }}>
                      <StatusIcon />
                      {item.isStatus && <BlinkingBadge />}

                      <Sys.Text style={styles.detailText}>
                        Tình trạng TTB
                      </Sys.Text>
                    </TouchableOpacity>
                  </View>
                  <View style={[styles.col]}>
                    <TouchableOpacity
                      onPress={() =>
                        dispatch(
                          setDetailedViewModalVisible({
                            show: false,
                            nextModal: 'isReportMissingModalVisible',
                          }),
                        )
                      }
                      disabled={!canReportMissing}
                      style={{
                        alignItems: 'center',
                        gap: 5,
                        opacity: canReportMissing ? 1 : 0.4,
                      }}>
                      <MissingIcon />
                      {item.isMissing && <BlinkingBadge />}
                      <Sys.Text style={styles.detailText}>Báo thiếu</Sys.Text>
                    </TouchableOpacity>
                  </View>
                  {canSeeChangeTag && (
                    <View style={[styles.col]}>
                      <TouchableOpacity
                        onPress={() =>
                          dispatch(
                            setDetailedViewModalVisible({
                              show: false,
                              nextModal: 'isTagChangeModalVisible',
                            }),
                          )
                        }
                        disabled={!canChangeTag}
                        style={{
                          alignItems: 'center',
                          gap: 5,
                          opacity: canChangeTag ? 1 : 0.4,
                        }}>
                        <ChangeIcon />
                        {item.tagChanged && <BlinkingBadge />}

                        <Sys.Text style={styles.detailText}>
                          {item?.tem_code ? 'Đổi tem' : 'Dán tem mới'}
                        </Sys.Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              )}
              <ScrollView style={{width: '100%', height: height / 3}}>
                <View style={[styles.card, styles.row]}>
                  <View style={styles.col}>
                    <View style={[styles.titleText, styles.titleTextDate]}>
                      <Sys.Text style={styles.detailText}>Tên kho:</Sys.Text>
                      <TouchableOpacity onPress={handleNavigation}>
                        <Sys.Text
                          style={[
                            styles.detailText,
                            isPosWarehouse ? {color: Color.red} : {},
                          ]}>
                          {item?.warehouse?.name}
                        </Sys.Text>
                      </TouchableOpacity>
                    </View>
                    <View style={[styles.titleText, styles.titleTextDate]}>
                      <Sys.Text style={styles.detailText}>Loại kho:</Sys.Text>
                      <Sys.Text style={styles.detailText}>
                        {item?.warehouse?.type_label}
                      </Sys.Text>
                    </View>
                    {isPosWarehouse && (
                      <View style={[styles.titleText, styles.titleTextDate]}>
                        <Sys.Text style={styles.detailText}>
                          Ngày bàn giao cho chủ điểm:
                        </Sys.Text>
                        <Sys.Text style={styles.detailText}>
                          {moment(item?.handover_date).format('DD/MM/YYYY')}
                        </Sys.Text>
                      </View>
                    )}

                    <View style={[styles.titleText, styles.titleTextDate]}>
                      <Sys.Text style={styles.detailText}>
                        Ngày đưa vào sử dụng:
                      </Sys.Text>
                      <Sys.Text
                        style={[styles.titleText, styles.titleTextDate]}>
                        {moment(item?.start_use_date).format('DD/MM/YYYY')}
                      </Sys.Text>
                    </View>
                    {item?.warranty_end_date && (
                      <View style={[styles.titleText, styles.titleTextDate]}>
                        <Sys.Text style={styles.detailText}>
                          Ngày hết bảo hành:
                        </Sys.Text>
                        <Sys.Text style={styles.detailText}>
                          {moment(item?.warranty_end_date).format('DD/MM/YYYY')}
                        </Sys.Text>
                      </View>
                    )}
                    {/*<View style={[styles.titleText, styles.titleTextDate]}>*/}
                    {/*  <Sys.Text style={styles.detailText}>*/}
                    {/*    Ngày tính phí bồi thường:*/}
                    {/*  </Sys.Text>*/}
                    {/*  <Sys.Text style={styles.detailText}>*/}
                    {/*    {moment(item?.compensation_date).format('DD/MM/YYYY')}*/}
                    {/*  </Sys.Text>*/}
                    {/*</View>*/}
                  </View>
                </View>
                <View style={styles.card}>
                  <Sys.Text style={[styles.detailText, styles.titleHeadText]}>
                    Lịch sử giao dịch
                  </Sys.Text>
                  <View style={styles.history}>
                    <Sys.Text style={[styles.historyHeadText, {minWidth: 10}]}>
                      #
                    </Sys.Text>
                    <Sys.Text style={styles.historyHeadText}>Mã tem</Sys.Text>
                    <Sys.Text style={[styles.historyHeadText, {minWidth: 80}]}>
                      Tên thiết bị
                    </Sys.Text>
                    <Sys.Text style={[styles.historyHeadText, {minWidth: 80}]}>
                      Mã hành động
                    </Sys.Text>
                    <Sys.Text style={styles.historyHeadText}>
                      Thời điểm
                    </Sys.Text>
                  </View>
                  <Divider />
                  {item.itemHistory &&
                    item.itemHistory.map((history, index) => (
                      <>
                        <View key={index} style={styles.historyRow}>
                          <Sys.Text
                            style={[styles.historyText, {minWidth: 10}]}>
                            {index + 1}
                          </Sys.Text>
                          <Sys.Text style={styles.historyText}>
                            {history.tem_code}
                          </Sys.Text>
                          <Sys.Text
                            style={[styles.historyText, {minWidth: 80}]}>
                            {history.name}
                          </Sys.Text>
                          <Sys.Text
                            style={[styles.historyText, {minWidth: 80}]}>
                            {history.action_label}
                          </Sys.Text>
                          <Sys.Text style={styles.historyText}>
                            {moment(history.actual_date).format('DD/MM/YYYY')}
                          </Sys.Text>
                        </View>
                        <Divider />
                      </>
                    ))}
                </View>

                {item?.tem_code && (
                  <View style={styles.card}>
                    <Sys.Text style={[styles.detailText, styles.titleHeadText]}>
                      Lịch sử thay tem
                    </Sys.Text>
                    <View style={styles.history}>
                      <Sys.Text
                        style={[styles.historyHeadText, {minWidth: 10}]}>
                        #
                      </Sys.Text>
                      <Sys.Text
                        style={[styles.historyHeadText, {minWidth: 60}]}>
                        Mã tem cũ
                      </Sys.Text>
                      <Sys.Text
                        style={[styles.historyHeadText, {minWidth: 60}]}>
                        Mã tem mới
                      </Sys.Text>
                      <Sys.Text
                        style={[styles.historyHeadText, {minWidth: 80}]}>
                        Thời điểm dán
                      </Sys.Text>
                      <Sys.Text style={styles.historyHeadText}>
                        Trạng thái
                      </Sys.Text>
                    </View>
                    <Divider />
                    {item.temHistory &&
                      item.temHistory.map((history, index) => (
                        <>
                          <View key={index} style={styles.historyRow}>
                            <Sys.Text
                              style={[styles.historyText, {minWidth: 10}]}>
                              {index + 1}
                            </Sys.Text>
                            <Sys.Text
                              style={[styles.historyText, {minWidth: 60}]}>
                              {history?.previous_tem_code || 'N/A'}
                            </Sys.Text>
                            <Sys.Text
                              style={[styles.historyText, {minWidth: 60}]}>
                              {history.tem_code}
                            </Sys.Text>
                            <Sys.Text
                              style={[styles.historyText, {minWidth: 80}]}>
                              {moment(history.actual_date).format(
                                'DD/MM/YYYY HH:mm:ss',
                              )}
                            </Sys.Text>
                            <Sys.Text style={styles.historyText}>
                              {history.tem_status_label}
                            </Sys.Text>
                          </View>
                          <Divider />
                        </>
                      ))}
                  </View>
                )}
              </ScrollView>
            </View>
          </Modal>
          <StatusUpdateModal item={item} onAction={onAction} />
          <ReportMissingModal item={item} onAction={onAction} />
          <TagChangeModal item={item} onAction={onAction} />
        </View>
      )}
      <TemInfoModal />
    </>
  );
});

export default DetailedViewModal;

const styles = StyleSheet.create({
  modalStyle: {
    padding: 0,
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#E7E7E7',
    padding: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
  },
  card: {
    marginTop: 10,
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 10,
  },
  col: {
    flexGrow: 1,
    flexBasis: 0,
  },
  col2p3: {
    flexGrow: 2,
    flexBasis: 0,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalText: {
    marginBottom: 10,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
  },
  titleText: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  titleTextDate: {
    marginBottom: 5,
  },
  detailText: {
    marginBottom: 5,
    fontSize: 14,
    fontWeight: 600,
  },
  titleHeadText: {
    color: '#001451',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },

  historyHeadText: {
    color: '#FFFFFF',
    fontSize: 12,
    flex: 1,
    textAlign: 'left',
    minWidth: 50,
  },
  historyText: {
    fontSize: 10,
    flex: 1,
    textAlign: 'left',
    minWidth: 50,
  },

  historyRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 5,
  },
  history: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    backgroundColor: '#0062FF',
    padding: 5,
  },
});
