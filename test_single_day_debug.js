// Test debug for single day API data
const React = require('react');

try {
  console.log('✅ Added comprehensive debug for single day API issue');
  console.log('✅ Debug will show:');
  console.log('   - API items count (should be 1 for single day)');
  console.log('   - API dates array (should show ["2025-06-16"])');
  console.log('   - formatData result structure');
  console.log('   - Empty data warning if formatData fails');
  console.log('');
  console.log('📊 Expected behavior with single day:');
  console.log('   - API items count: 1');
  console.log('   - API dates: ["2025-06-16"]');
  console.log('   - formatData should still create chart arrays');
  console.log('   - Chart should display single day data');
  console.log('');
  console.log('🔍 Possible issues to identify:');
  console.log('   1. formatData requires minimum days?');
  console.log('   2. Chart component expects 7 days?');
  console.log('   3. Games array not available when formatData runs?');
  console.log('   4. API data structure different?');
  console.log('');
  console.log('✅ All syntax validated successfully');
  console.log('🎉 Single day debug ready!');
  console.log('📋 Check console for API data count and structure');
} catch (error) {
  console.error('❌ Error:', error.message);
}
